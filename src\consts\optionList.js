import { isBlank } from '@/utils/validate'

// 抄表周期下拉
export const meterReadingCycleOptions = [
	{ label: '每月', value: 0 },
	{ label: '单月', value: 1 },
	{ label: '双月', value: 2 },
]

// 表册类型
export const bookTypeOptions = [
	{ label: '企业', value: 1 },
	{ label: '居民', value: 2 },
]

// 抄表次数
export const meterReadingNumberOptions = [
	{ label: '1', value: 1 },
	{ label: '2', value: 2 },
	{ label: '3', value: 3 },
]

// 贸易结算类型
export const archivesMeterTypeOptions = [
	{ label: '非贸易结算表', value: 0 },
	{ label: '贸易结算表', value: 1 },
]

// 抄表任务状态
export const taskStatusOptions = [
	{ label: '待抄表', value: 0 },
	{ label: '抄表中', value: 1 },
	{ label: '已抄表', value: 2 },
	{ label: '已关闭', value: 3 },
]

// 是否全部移交
export const handOverOptions = [
	{ label: '否', value: 0 },
	{ label: '是', value: 1 },
]
// 抄表状态
export const readingStatusOptions = [
	{ label: '待抄表', value: 0 },
	{ label: '已抄表', value: 1 },
	{ label: '自动审核异常', value: 2 },
	{ label: '自动审核通过', value: 3 },
	{ label: '复核通过', value: 4 },
	{ label: '复核驳回', value: 5 },
]
// 抄表情况
export const checkStatusOptions = [
	{ label: '正常', value: 0 },
	{ label: '自抄', value: 1 },
	{ label: '表坏', value: 2 },
	{ label: '物压', value: 3 },
	{ label: '门堵', value: 4 },
	{ label: '下水', value: 5 },
	{ label: '深埋', value: 6 },
	{ label: '锁门', value: 7 },
	{ label: '失灵', value: 8 },
	{ label: '黑面', value: 9 },
	{ label: '定期换表', value: 10 },
	{ label: '故障换表', value: 11 },
	{ label: '换表没数', value: 12 },
	{ label: '闭拴', value: 13 },
	{ label: '准停', value: 14 },
	{ label: '没有', value: 15 },
	{ label: '正付', value: 16 },
	{ label: '季节', value: 17 },
	{ label: '消防', value: 18 },
	{ label: '指针无关', value: 19 },
	{ label: '满量程', value: 20 },
	{ label: '水表倒装', value: 21 },
	{ label: '漏抄', value: 22 },
	{ label: '异常容错', value: 23 },
]
// 特殊抄表状态值集合 这些状态 本次指针、本次水量字段非必填
export const specialCheckStatus = [13, 14, 15, 16, 17, 18, 22, 23]
// 抄表情况对应计算方法
export const CHECK_STATUS_CALC_METHODS = {
	// 默认
	default: data => {
		let { curMeterReading, lastMeterReading } = data

		let useAmount = undefined
		if (!isBlank(curMeterReading)) {
			lastMeterReading = lastMeterReading || 0
			useAmount = curMeterReading - lastMeterReading
		}

		return {
			curMeterReading,
			useAmount,
		}
	},
	// 水表倒装：上次指针-本次指针
	21: data => {
		let { curMeterReading, lastMeterReading } = data
		let useAmount = undefined
		if (!isBlank(curMeterReading)) {
			lastMeterReading = lastMeterReading || 0
			useAmount = lastMeterReading - curMeterReading
		}

		return {
			curMeterReading,
			useAmount,
		}
	},
	// 满量程：量程-上次指针+本次指针（无量程不算）
	20: data => {
		let { curMeterReading, lastMeterReading, range } = data
		let useAmount = undefined
		if (!isBlank(curMeterReading) && !isBlank(range)) {
			lastMeterReading = lastMeterReading || 0
			useAmount = range - lastMeterReading + curMeterReading + 1
		}

		return {
			curMeterReading,
			useAmount,
		}
	},
	// 没有: 上次指针覆盖本次指针，并且水量为0
	15: data => {
		let { lastMeterReading } = data
		return {
			curMeterReading: lastMeterReading,
			useAmount: 0,
		}
	},
	// 失灵：上次指针覆盖本次指针，水量手输）
	8: data => {
		let { lastMeterReading } = data
		return {
			curMeterReading: lastMeterReading,
			useAmount: undefined,
		}
	},
}

// 表卡类型
export const userTypeOptions = [
	{ label: '企业表卡', value: 4 },
	{ label: '居民表卡', value: 3 },
]

// 账项状态
export const billStatusOptions = [
	{ label: '在用', value: 1 },
	{ label: '停用', value: 0 },
]

// 分配方式
export const distributionModeOptions = [
	{ label: '占比分配', value: 0 },
	{ label: '固定量分配', value: 1 },
	{ label: '总量占比+固定量分配', value: 2 },
	{ label: '固定量+剩余量占比分配', value: 4 },
	{ label: '不分配', value: 3 },
]

// 子分配方式
export const subDistributionModeOptions = [
	{
		label: '总量占比（%）',
		value: 0,
	},
	{
		label: '固定量（吨）',
		value: 1,
	},
	{
		label: '剩余量占比（%）',
		value: 2,
	},
]
// 离职状态
export const resignedTypeOptions = [
	{
		value: false,
		label: '在职',
	},
	{
		value: true,
		label: '离职',
	},
]
// 员工状态
export const staffStatusOptions = [
	{
		value: 0,
		label: '在职',
	},
	{
		value: 1,
		label: '离职',
	},
]

// 是否
export const yesOrNoOptions = [
	{ label: '是', value: 1 },
	{ label: '否', value: 0 },
]
