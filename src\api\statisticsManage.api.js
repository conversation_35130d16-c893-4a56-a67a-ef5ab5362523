import service from './request'
import { CPM } from '@/consts/moduleNames'

// 报表配置
// 报表分类列表
export function queryReportCategoryList(data) {
	return service({
		url: `${CPM}/report/category/queryCategory`,
		method: 'post',
		data,
	})
}
// 新增报表分类
export function addReportCategory(data) {
	return service({
		url: `${CPM}/report/category/add`,
		method: 'post',
		data,
	})
}
// 修改报表分类
export function editReportCategory(data) {
	return service({
		url: `${CPM}/report/category/update`,
		method: 'post',
		data,
	})
}
// 删除报表分类
export function deleteReportCategory(params) {
	return service({
		url: `${CPM}/report/category/delete`,
		method: 'get',
		params,
	})
}

// 报表分页查询
export function queryReportPage(data) {
	return service({
		url: `${CPM}/report/config/queryPage`,
		method: 'post',
		data,
	})
}
// 新增报表
export function addReport(data) {
	return service({
		url: `${CPM}/report/config/add`,
		method: 'post',
		data,
	})
}
// 修改报表
export function editReport(data) {
	return service({
		url: `${CPM}/report/config/update`,
		method: 'post',
		data,
	})
}
// 删除报表
export function deleteReport(params) {
	return service({
		url: `${CPM}/report/config/delete`,
		method: 'get',
		params,
	})
}

// 分类报表tree数据
export function reportTreeList(data) {
	return service({
		url: `${CPM}/report/config/permissionList`,
		method: 'post',
		data,
	})
}

// 报表角色
// 报表角色列表
export function queryReportRoleList(params) {
	return service({
		url: `${CPM}/report/role/queryRoles`,
		method: 'get',
		params,
	})
}
// 新增报表角色
export function addReportRole(data) {
	return service({
		url: `${CPM}/report/role/add`,
		method: 'post',
		data,
	})
}
// 修改报表角色
export function editReportRole(data) {
	return service({
		url: `${CPM}/report/role/update`,
		method: 'post',
		data,
	})
}
// 删除报表角色
export function deleteReportRole(params) {
	return service({
		url: `${CPM}/report/role/delete`,
		method: 'get',
		params,
	})
}
// 所有报表列表树
export function queryCategoryReportList(data) {
	return service({
		url: `${CPM}/report/category/queryCategoryReportList`,
		method: 'post',
		data,
	})
}
// 角色设置报表
export function saveReportsToRole(data) {
	return service({
		url: `${CPM}/report/roleResource/savePermission`,
		method: 'post',
		data,
	})
}

// 根据角色id获取已绑定人员列表
export function queryRoleUsers(data) {
	return service({
		url: `${CPM}/report/roleUserRef/queryRoleUsers`,
		method: 'post',
		data,
	})
}
// 角色用户配置解绑
export function removeRoleUsers(data) {
	return service({
		url: `${CPM}/report/roleUserRef/delete`,
		method: 'post',
		data,
	})
}
// 角色配置用户
export function addRoleUsers(data) {
	return service({
		url: `${CPM}/report/roleUserRef/add`,
		method: 'post',
		data,
	})
}
// 选择用户下拉框（排除当前角色已经选择的人员）
export function queryRoleStaff(data) {
	return service({
		url: `${CPM}/report/roleUserRef/queryStaff`,
		method: 'post',
		data,
	})
}

// 查询模板列表
export function queryQueryTemplateList(data) {
	return service({
		url: `${CPM}/report/category/queryCategory`,
		method: 'post',
		data,
	})
}
// 新增查询模板
export function addQueryTemplate(data) {
	return service({
		url: `${CPM}/report/category/add`,
		method: 'post',
		data,
	})
}
// 获取查询模板配置详情
export function getQueryTemplateDetail(data) {
	return service({
		url: `${CPM}/report/category/add`,
		method: 'post',
		data,
	})
}
// 修改查询模板名称
export function updateQueryTemplateName(data) {
	return service({
		url: `${CPM}/report/category/update`,
		method: 'post',
		data,
	})
}
// 修改查询模板
export function editQueryTemplate(data) {
	return service({
		url: `${CPM}/report/category/update`,
		method: 'post',
		data,
	})
}
// 删除查询模板
export function deleteQueryTemplate(params) {
	return service({
		url: `${CPM}/report/category/delete`,
		method: 'get',
		params,
	})
}

// 执行组合查询
export function executeTemplateQuery(data) {
	return service({
		url: `${CPM}/report/config/queryPage`,
		method: 'post',
		data,
	})
}
// 导出组合查询结果
export function exportTemplateQueryData(params) {
	return service({
		url: `${CPM}/report/role/delete`,
		method: 'get',
		params,
	})
}

// 查询报表数据
export function queryReportInfo(params) {
	return service({
		url: `${CPM}/report/config/queryReportInfo`,
		method: 'get',
		params,
	})
}
