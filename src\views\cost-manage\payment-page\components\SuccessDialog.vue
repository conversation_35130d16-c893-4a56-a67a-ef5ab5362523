<template>
	<gc-el-dialog :show.sync="visible" :show-footer="false">
		<div class="box-tip">
			<img src="@/assets/images/pic/upload-success.png" alt="" />
			<h3 class="text">
				<span>缴费成功</span>
			</h3>
		</div>
		<div class="box-info">
			<div class="item">
				<h3 class="text">已缴费</h3>
				<p class="amount">
					<span class="bold">{{ amount.value.toFixed(2) }}</span>
					{{ amount.unit }}
				</p>
			</div>
		</div>

		<div class="box-invoice-tips">
			<GcTips>存在罚没款账单，无法进行开票</GcTips>
		</div>
		<div class="box-btns">
			<button class="gc-button gc-button-one" type="button" @click="$emit('go-on')">继续</button>
			<button
				v-has="'payment_invoice_merge-open-invoice6'"
				v-if="!invoiceOpenDisabled"
				class="gc-button gc-button-two"
				type="button"
				@click="$emit('print-invoice', 'merge')"
			>
				合并开票
			</button>
			<button
				v-has="'payment_invoice_batch-open-invoice2'"
				v-if="!invoiceOpenDisabled"
				class="gc-button gc-button-two"
				type="button"
				@click="$emit('print-invoice', 'batch')"
			>
				批量开票
			</button>
			<button class="gc-button gc-button-two" type="button" @click="$emit('print-fee-detail')">
				收费明细打印
			</button>
		</div>
	</gc-el-dialog>
</template>

<script>
import GcTips from '@/components/UploadTip'
// src\components\UploadTip\index.vue
export default {
	name: 'SuccessDialog',
	components: {
		GcTips,
	},
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		amount: {
			type: Object,
			default: () => {},
		},
		invoiceOpenDisabled: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {}
	},
	computed: {
		visible: {
			get() {
				return this.show
			},
			set(val) {
				this.$emit('update:show', val)
			},
		},
	},
}
</script>
<style lang="scss" scoped>
::v-deep {
	.gc-dialog-custom .el-dialog__header {
		display: none;
	}
	.el-dialog__body {
		padding: 3rem 0 !important;
	}
}
.box-tip {
	text-align: center;
	.text {
		color: $base-color-6;
		padding: 20px 0;
		font-size: 27px;
		font-weight: bold;
		span {
			display: block;
			line-height: 20px;
		}
		span + span {
			margin-top: 10px;
		}
	}
}
.box-info {
	background: #f8f9fe;
	padding: 20px;
	text-align: center;
	display: flex;
	.item {
		flex: 1;
		display: flex;
		align-items: flex-end;
		justify-content: center;
		gap: 15px;
	}
	.text {
		color: $base-color-6;
	}
	.amount {
		margin-top: 10px;
		.bold {
			font-size: 32px;
			color: #222;
			font-weight: 600;
			margin-right: 8px;
		}
	}
}
.box-btns {
	@include flex-center;
	margin-top: 30px;
	.gc-button {
		margin: 0 10px;
	}
}
.box-invoice-tips {
	display: flex;
	flex-direction: row;
	justify-content: center;
	padding: 12px;
	/deep/.tipsContainer {
		background: rgb(253, 226, 226);
		.el-icon-bell,
		.content {
			color: rgb(245, 108, 108);
		}
	}
}
</style>
