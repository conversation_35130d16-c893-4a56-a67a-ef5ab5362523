import { bookTypeOptions } from '@/consts/optionList.js'

const baseSelect = (opt = {}) => {
	const { multiple = true, options } = opt
	const config = {
		operator: multiple ? ['in', 'not in'] : ['=', '!='],
		input: {
			type: 'el-select',
			options: options || [],
			attrs: {
				multiple,
				clearable: true,
				filterable: true,
			},
		},
	}
	if (!options) {
		config.__putOptions = function (options) {
			this.input.options = options
		}
	}
	return config
}

const baseInput = {
	operator: ['=', '!=', '包含', '不包含'],
	input: {
		type: 'el-input',
		attrs: {
			clearable: true,
		},
	},
}

const baseNumber = {
	operator: ['=', '!=', '>', '>=', '<', '<='],
	input: {
		type: 'el-input-number',
	},
}

const baseDate = (opt = {}) => {
	const { format = 'yyyy-MM-dd', type = 'date' } = opt
	return {
		operator: ['=', '!=', '>', '>=', '<', '<='],
		input: {
			type: 'el-date-picker',
			attrs: {
				format,
				valueFormat: format,
				placeholder: '选择日期',
				clearable: true,
				type,
			},
		},
	}
}

export default instance => {
	const { virtualMeterType = [], payMode = [], payChannel = [], billStatus = [], archiveState = [], billType = [] } =
		instance.$store.getters.dataList || {}
	return [
		{
			key: 'orgCode',
			label: '营业所分公司',
			view: ['meter', 'reading', 'bill', 'payment'],
			...baseSelect({ options: instance.$store.getters.orgList }),
		},
		{
			key: 'archivesIdentity',
			label: '表卡编号',
			view: ['meter', 'reading', 'bill', 'payment'],
			...baseInput,
		},
		{
			key: 'bookNo',
			label: '表册编号',
			view: ['meter', 'reading', 'bill', 'payment'],
			...baseSelect(),
		},
		{
			key: 'alleyId',
			label: '坊别',
			view: ['meter', 'reading', 'bill', 'payment'],
			...baseSelect(),
		},
		{
			key: 'priceNatureId',
			label: '用水性质',
			view: ['meter', 'bill', 'payment'],
			operator: ['in', 'not in'],
			input: {
				type: 'el-cascader',
				options: [],
				attrs: {
					clearable: true,
					filterable: true,
					showAllLevels: false,
				},
				props: {
					multiple: true,
					label: 'natureName',
					value: 'priceNatureId',
					emitPath: false,
					checkStrictly: true,
				},
			},
			__putOptions: function (options) {
				this.input.options = options
			},
		},
		{
			key: 'tapWaterCode',
			label: '自来水编号',
			view: ['meter'],
			...baseInput,
		},
		{
			key: 'userType',
			label: '用户类型',
			view: ['meter', 'reading', 'bill', 'payment'],
			...baseSelect({ multiple: false, options: bookTypeOptions }),
		},
		{
			key: 'virtualMeterType',
			label: '表卡类型',
			view: ['meter', 'reading', 'bill', 'payment'],
			...baseSelect({
				multiple: false,
				options: virtualMeterType.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}),
			}),
		},
		{
			key: 'archivesStatus',
			label: '表卡状态',
			view: ['meter', 'reading'],
			...baseSelect({
				options: archiveState.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}),
			}),
		},
		{
			key: 'meterTypeId',
			label: '水表类型',
			view: ['meter'],
			...baseSelect({ multiple: false }),
		},
		{
			key: 'archivesTime',
			label: '创建日期',
			view: ['meter'],
			...baseDate(),
		},
		{
			key: 'enterpriseNumber',
			label: '企业编号',
			view: ['meter', 'reading', 'bill', 'payment'],
			...baseSelect(),
		},
		{
			key: 'baseMeterNo',
			label: '水表标号',
			view: ['meter'],
			...baseInput,
		},
		{
			key: 'stopDate',
			label: '停用日期',
			view: ['meter'],
			...baseDate(),
		},
		{
			key: 'cancellationDate',
			label: '销卡日期',
			view: ['meter'],
			...baseDate(),
		},
		{
			key: 'billDate',
			label: '账期',
			view: ['bill', 'payment'],
			...baseDate({ format: 'yyyy-MM' }),
		},
		{
			key: 'billOpenTime',
			label: '开账日期',
			view: ['bill'],
			...baseDate(),
		},
		{
			key: 'billStatus',
			label: '账单状态',
			view: ['bill'],
			...baseSelect({
				options: billStatus,
			}),
		},
		{
			key: 'billType',
			label: '账单类型',
			view: ['bill'],
			...baseSelect({
				options: billType.map(item => {
					return {
						label: item.sortName,
						value: Number(item.sortValue),
					}
				}),
			}),
		},
		{
			key: 'clearedType',
			label: '销账类型',
			view: ['payment'],
			...baseSelect({
				multiple: false,
				options: [
					{
						label: '销账',
						value: 1,
					},
					{
						label: '预存余额销账',
						value: 2,
					},
				],
			}),
		},
		{
			key: 'taskYear',
			label: '抄表年',
			view: ['reading'],
			...baseDate({ format: 'yyyy' }),
		},
		{
			key: 'taskMonths',
			label: '抄表月',
			view: ['reading'],
			...baseSelect({
				options: [
					{
						value: '1',
						label: '1月',
					},
					{
						value: '2',
						label: '2月',
					},
					{
						value: '3',
						label: '3月',
					},
					{
						value: '4',
						label: '4月',
					},
					{
						value: '5',
						label: '5月',
					},
					{
						value: '6',
						label: '6月',
					},
					{
						value: '7',
						label: '7月',
					},
					{
						value: '8',
						label: '8月',
					},
					{
						value: '9',
						label: '9月',
					},
					{
						value: '10',
						label: '10月',
					},
					{
						value: '11',
						label: '11月',
					},
					{
						value: '12',
						label: '12月',
					},
				],
			}),
		},
		{
			key: 'meterReadingStaffIds',
			label: '抄表员',
			view: ['reading', 'bill', 'payment'],
			...baseSelect(),
		},
		{
			key: 'useAmount',
			label: '水量',
			view: ['reading'],
			...baseNumber,
		},
		{
			key: 'payMode',
			label: '付款方式',
			view: ['payment'],
			...baseSelect({ options: payMode }),
		},
		{
			key: 'payChannel',
			label: '缴费渠道',
			view: ['payment'],
			...baseSelect({ options: payChannel }),
		},
		{
			key: 'payTime',
			label: '缴费时间',
			view: ['payment'],
			...baseDate({ type: 'datetime' }),
		},
	]
}
