<template>
	<CardContainer :cardDetail="cardDetail">
		<div class="content-box">
			<div class="top">
				<el-select v-model="selValue" placeholder="请选择" @change="getList">
					<el-option
						v-for="item in natureOptions"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					></el-option>
				</el-select>
				<el-radio-group v-model="tabValue" @input="getList">
					<el-radio-button :label="0">月</el-radio-button>
					<el-radio-button :label="1">年</el-radio-button>
				</el-radio-group>
			</div>
			<div class="bottom">
				<Gcline1
					:unit="chartOptions.unit"
					:xData="chartOptions.xData"
					:seriesData="chartOptions.seriesData"
					:seriesName="chartOptions.seriesName"
					:startDataZoom="50"
					:endDataZoom="60"
				/>
			</div>
		</div>
	</CardContainer>
</template>

<script>
import CardContainer from './CardContainer.vue'
import { apiGetWaterPayCostChart } from '@/api/home.api'
export default {
	name: 'WaterCollection',
	components: { CardContainer },
	props: {
		natureOptions: {
			type: Array,
			default: () => [],
		},
		orgCode: {
			type: String,
			default: '',
		},
	},
	watch: {
		natureOptions: {
			handler: function (arr) {
				if (arr && arr.length) {
					this.selValue = arr[0].value
				}
			},
			deep: true,
		},
		orgCode() {
			this.getList()
		},
		'cardDetail.activeTab'(v) {
			this.getList()
		},
	},
	data() {
		return {
			cardDetail: {
				titleList: [
					{
						label: '当月水费回收率',
						value: '--',
						unit: '%',
					},
					{
						label: '全年水费回收率',
						value: '--',
						unit: '%',
					},
				],
				activeTab: '3',
				tabList: [
					{
						name: '居民表卡',
						value: '3',
					},
					{
						name: '企业表卡',
						value: '4',
					},
				],
				bg: require('@/assets/images/bg/home-bg3.png'),
				name: '水费回收率',
			},
			tabValue: 0,
			selValue: '',
			chartOptions: {
				unit: '单位: %',
				xData: [],
				seriesData: [],
				seriesName: '',
			},
		}
	},
	computed: {},
	created() {},
	methods: {
		async getList() {
			try {
				const { monthSameRatio, yearSameRatio, naturePayAmtList } = await apiGetWaterPayCostChart({
					dimension: this.tabValue,
					orgCode: this.orgCode,
					userType: this.cardDetail.activeTab,
				})
				this.cardDetail.titleList[0].value = monthSameRatio
				this.cardDetail.titleList[1].value = yearSameRatio
				const typeMap = this.cardDetail.tabList.reduce((map, item) => {
					map[item.value] = item.name.replace('表卡', '')
					return map
				}, {})
				this.chartOptions.seriesName = typeMap[this.cardDetail.activeTab] || ''

				if (Array.isArray(naturePayAmtList) && naturePayAmtList.length) {
					const result = naturePayAmtList.find(item => item.natureNo === this.selValue)
					if (result && Array.isArray(result.children) && result.children.length) {
						this.chartOptions.xData = result.children.map(item => item.natureName)
						const data = result.children.map(item => item.useAmount)
						this.chartOptions.xData.unshift(result.natureName)
						data.unshift(result.useAmount)
						this.chartOptions.seriesData = [
							{
								name: this.chartOptions.seriesName,
								data,
								type: 'line',
								smooth: true,
								color: '#FF928A',
								areaStyle: {
									color: new window.echarts.graphic.LinearGradient(0, 0, 0, 1, [
										{ offset: 0, color: 'rgba(255, 146, 138, 0.3)' },
										{ offset: 1, color: 'rgba(255, 146, 138, 0.05)' },
									]),
								},
							},
						]
					}
				}
			} catch (error) {
				console.error(error)
			}
		},
	},
}
</script>
