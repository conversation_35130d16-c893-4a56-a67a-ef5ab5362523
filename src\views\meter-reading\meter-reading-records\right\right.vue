<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-09-10 16:12:11
 * @LastEditors: houyan
 * @LastEditTime: 2024-09-11 16:39:59
-->
<template>
	<div class="right-container">
		<!-- <el-button type="primary" class="import-btn">抄表结果批量导入</el-button> -->
		<div class="table-container">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handlePageChange"
			>
				<template #imageUrl="{ row }">
					<UploadImgSimple v-model="row.imageUrl" />
				</template>
				<template #operate="{ row }">
					<el-button
						type="text"
						size="small"
						@click="handleView(row)"
						v-has="'plan-collection_meterReadingTask_pageMeterRecordModifyLog'"
						v-if="row.haveRecordModifyLog"
					>
						查看水量修改记录
					</el-button>
				</template>
			</GcTable>
		</div>
	</div>
</template>

<script>
import UploadImgSimple from '@/components/UploadImgSimple'
import { getRecordList } from '@/api/meterReading.api.js'

export default {
	name: '',
	components: { UploadImgSimple },
	props: {
		params: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		return {
			loading: false,
			columns: [
				{
					key: 'bookNo',
					name: '表册编号',
					tooltip: true,
				},
				{
					key: 'archivesIdentity',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'addressFullName',
					name: '地址',
					minWidth: 250,
					tooltip: true,
				},
				{
					key: 'lastMeterReading',
					name: '上次指针',
					tooltip: true,
				},
				{
					key: 'curMeterReading',
					name: '本次指针',
					tooltip: true,
				},
				{
					key: 'useAmount',
					name: '本次水量',
					tooltip: true,
				},
				{
					key: 'checkStatusDesc',
					name: '抄表情况',
					tooltip: true,
				},
				{
					key: 'readingStatusDesc',
					name: '抄表状态',
					tooltip: true,
				},
				{
					key: 'thisRecordDate',
					name: '抄表日期',
					minWidth: 180,
				},
				{
					key: 'imageUrl',
					name: '抄表照片',
				},
				{
					key: '55',
					name: '抄表经纬度',
					tooltip: true,
					render: (h, row) => {
						const { longitude, latitude } = row
						return h('div', {}, `${longitude ?? '-'}${longitude && latitude ? ',' : ''}${latitude ?? '-'}`)
					},
				},
				{
					key: 'meterReadingStaffName',
					name: '抄表员',
					tooltip: true,
				},
				{
					key: 'operate',
					name: '操作',
					fixed: 'right',
					width: 120,
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},
	computed: {},
	created() {},
	methods: {
		handlePageChange({ page, size }) {
			this.pageData.current = page
			this.pageData.size = size
			this.getList()
		},
		async getList(curPage) {
			this.loading = true

			if (curPage) {
				this.pageData.current = curPage
			}

			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await getRecordList({
					current,
					size,
					...this.params,
				})
				this.tableData = records
				this.pageData.total = total
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		resetTableData() {
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
		},
		// 查看操作
		handleView(row) {
			console.log('查看', row)
			this.$router.push({
				path: '/meterReading/meterReadingRecordDetail',
				params: {
					rowData: row, // 传递完整的 row 数据
				},
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.right-container {
	width: 0;
	flex: 1;
	padding: 20px;
	background-color: #fff;
	display: flex;
	flex-direction: column;
}
.import-btn {
	align-self: flex-end;
	width: 120px;
	margin-bottom: 20px;
}
.table-container {
	flex: 1;
	height: 0;
}
</style>
