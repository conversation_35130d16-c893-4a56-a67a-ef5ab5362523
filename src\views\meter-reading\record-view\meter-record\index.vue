<!--
 * @Description: 
 * @Version: 
 * @Autor: houyan
 * @Date: 2024-09-02 19:07:24
 * @LastEditors: houyan
 * @LastEditTime: 2024-09-27 15:55:56
-->
<template>
	<div class="container">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<el-form-item>
				<el-button type="primary" @click="getList(1)">筛选</el-button>
				<el-button @click="handleReset">重置</el-button>
				<el-button
					v-if="selectedData.length > 0"
					style="margin-left: 20px"
					type="primary"
					@click="handleBatchPrint"
				>
					批量打印
				</el-button>
			</el-form-item>
		</GcFormSimple>
		<div class="table-container">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				needType="selection"
				@selectChange="selectChange"
				showPage
				@current-page-change="handlePageChange"
			>
				<template v-slot:deal="{ row }">
					<el-button
						v-has="'plan-collection_meterReadingBook_updateArchivesSeq'"
						type="text"
						size="medium"
						@click="handleModifyNo(row)"
					>
						修改册内序号
					</el-button>
					<el-button
						v-has="'plan-collection_meterReadingBook_meterPrint'"
						type="text"
						size="medium"
						@click="handlePrintMeterCard(row)"
					>
						抄表卡打印
					</el-button>
				</template>
			</GcTable>
		</div>

		<!-- 修改册内序号 -->
		<UpdateSeqDialog
			ref="updateSeqDialogRef"
			:show.sync="showUpdateSeq"
			:id="currentArchivesId"
			@success="getList(1)"
		/>
	</div>
</template>

<script>
import UpdateSeqDialog from './components/updateSeqDialog.vue'
import { getColumn } from './tableColumn.js'
import { meterCardPrint } from '@/views/print/meter/index.js'
import { getBookRecordList } from '@/api/meterReading.api.js'

export default {
	name: '',
	components: { UpdateSeqDialog },
	data() {
		return {
			formData: {
				archivesIdentity: '',
			},
			formItems: [
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
					attrs: {
						clearable: true,
						placeholder: '请输入表卡编号',
					},
				},
			],
			formAttrs: {
				inline: true,
				labelWidth: '80px',
			},
			loading: false,
			columns: getColumn(this),
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},

			// 修改册内序号
			showUpdateSeq: false,
			currentArchivesId: '',
			selectedData: [],
		}
	},
	computed: {},
	created() {
		this.getList()
	},
	methods: {
		// 多选
		selectChange(arr) {
			console.log(arr)
			this.selectedData = arr
		},
		handleReset() {
			this.$refs.formRef.resetForm()
			this.getList(1)
		},
		handleModifyNo(row) {
			this.currentArchivesId = row.archivesId
			this.$refs.updateSeqDialogRef.setFormData(row)
			this.showUpdateSeq = true
		},

		handlePageChange({ page, size }) {
			this.pageData.current = page
			this.pageData.size = size
			this.getList()
		},
		async getList(curPage) {
			const bookId = this.$route.query.bookId
			if (!bookId) return

			if (curPage) {
				this.pageData.current = curPage
			}

			this.loading = true

			try {
				const { current, size } = this.pageData
				const { total = 0, records = [] } = await getBookRecordList({
					bookId,
					archivesIdentity: this.formData.archivesIdentity,
					current,
					size,
				})
				this.tableData = records
				this.pageData.total = total
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},

		// 抄表卡打印
		handlePrintMeterCard(row) {
			meterCardPrint('plan-collection_meterReadingBook_meterPrint', row.archivesId, this)
		},
	},
}
</script>

<style lang="scss" scoped>
.container {
	padding: 20px;
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
}

.table-container {
	flex: 1;
	height: 0;
}
</style>
