<template>
	<div class="container">
		<div class="base-info">
			<GcModelHeader title="待转化表卡信息" :icon="require('@/assets/images/icon/title-circle-check.png')" />
			<GcGroupDetail :data="userData"></GcGroupDetail>
		</div>
		<div class="border-box"></div>
		<GcModelHeader title="转化操作" :icon="require('@/assets/images/icon/title-circle-check.png')" />
		<div class="operate" v-if="show">
			<MeterSteps class="meter-container" :active="step" :list="stepList" />
			<StepOne ref="stepOneRef" v-show="step === 1" :detail="detailData" />
			<StepTwo ref="stepTwoRef" v-show="step === 2" />
			<StepThree ref="stepThreeRef" v-show="step === 3" :userType="userType" />
			<StepFour ref="stepFourRef" v-show="step === 4" :userType="userType" />
			<StepFive ref="stepFiveRef" v-show="step === 5" :userType="userType" />
			<div class="button-group">
				<el-button v-show="step !== 1" @click="prevStep">上一步</el-button>
				<el-button v-show="step !== stepList.length" type="primary" @click="nextStep">下一步</el-button>
				<el-button v-show="step === stepList.length" type="primary" @click="handleSubmit">开始转换</el-button>
			</div>
		</div>
		<div class="empty" v-else>
			<img src="@/assets/images/pic/empty.png" alt="" />
			<span>{{ emptyTip }}</span>
		</div>
	</div>
</template>

<script>
import { getfilterName } from '@/utils'
import {
	apiGetArchivesDetail,
	apiArchivesTransferTablecard,
	apiArchivesTransferTablecard2,
} from '@/api/meterManage.api'
import MeterSteps from './MeterSteps.vue'
import StepOne from './step-one/index.vue'
import StepTwo from './step-two/index.vue'
import StepThree from './step-three/index.vue'
import StepFour from './step-four/index.vue'
import StepFive from './step-five/index.vue'
export default {
	name: '',
	components: { MeterSteps, StepOne, StepTwo, StepThree, StepFour, StepFive },
	data() {
		return {
			stepList: [
				{
					step: 1,
					text: '选择转换方式',
				},
				{ step: 2, text: '选择册本' },
				{ step: 3, text: '用户信息填写' },
				{ step: 4, text: '开票信息填写' },
				{ step: 5, text: '新表卡信息填写' },
			],
			step: 1,
			show: false,
			detailData: {},
			userType: 3,
		}
	},
	computed: {
		// 用户信息
		userData() {
			const list = [
				{
					key: '档案类型',
					value: '--',
					field: 'userType',
				},
				{
					key: '表卡编号',
					value: '--',
					field: 'archivesIdentity',
				},
				{
					key: '表卡状态',
					value: '--',
					field: 'archivesStatus',
				},
				{
					key: '册本编号',
					value: '--',
					field: 'bookNo',
				},
				{
					key: '册内序号',
					value: '--',
					field: 'recordSeq',
				},
				{
					key: '表具地址',
					value: '--',
					field: 'addressFullName',
				},
				{
					key: '抄表员',
					value: '--',
					field: 'meterReadingStaffName',
				},
				{
					key: '用水性质',
					value: '--',
					field: 'natureName',
				},
				{
					key: '虚表表卡数量',
					value: '--',
					field: 'virtualArchivesCount',
				},
				{
					key: '欠费总金额（含虚表欠费）',
					value: '--',
					field: 'arrearsTotalAmount',
				},
				{
					key: '账户余额',
					value: '--',
					field: 'meterBalanceAmount',
				},
			]
			const newData = Object.assign({}, ...Object.values(this.detailData))
			const getValue = (field, value) => {
				const { userType = [], archiveState = [] } = this.$store.getters.dataList || {}
				switch (field) {
					case 'userType':
						return getfilterName(userType, value, 'sortValue', 'sortName')
					case 'archivesStatus':
						return getfilterName(archiveState, value, 'sortValue', 'sortName')
					default:
						return value
				}
			}
			list.forEach(item => {
				item.value = getValue(item.field, newData[item.field])
			})
			return {
				list,
				row: 6,
			}
		},
		emptyTip() {
			const newData = Object.assign({}, ...Object.values(this.detailData))
			let str = ''
			if (newData.arrearsTotalAmount) {
				str = '存在欠费，无法操作...'
			}
			return str
		},
	},
	activated() {
		const { archivesId } = this.$route.query
		if (archivesId) {
			this._apiGetArchivesDetail({ archivesId })
		}
	},
	methods: {
		// 获取待转化表卡信息
		_apiGetArchivesDetail(data) {
			apiGetArchivesDetail(data)
				.then(res => {
					this.detailData = res
					const newData = Object.assign({}, ...Object.values(this.detailData))
					if (newData.arrearsTotalAmount) {
						this.show = false
					} else {
						this.show = true
					}
				})
				.catch(() => {})
		},
		async validateStep() {
			if (this.step === 1) {
				const selectedType = this.$refs.stepOneRef.selectedType
				if (this.userType !== selectedType) {
					this.userType = selectedType
					this.resetAllData()
				}
				this.$refs.stepTwoRef.form.bookType = selectedType === 4 ? 1 : 2
			} else if (this.step === 2) {
				const { bookId, orgCode, alleyCode } = this.$refs.stepTwoRef.currentBookInfo
				if (!bookId) {
					this.$message.error('未选择册本')
					return false
				}
				this.$refs.stepThreeRef._apiGetCompany(orgCode)
				this.$refs.stepFiveRef.params.alleyCode = alleyCode
				this.$refs.stepFiveRef.params.orgCode = orgCode
				this.$refs.stepFiveRef.params.bookId = bookId
				this.$refs.stepFiveRef.setArchivesIdentityRule()
			} else if (this.step === 3) {
				const flag = await this.$refs.stepThreeRef.validateForm()
				if (!flag) {
					this.$message.error('用户信息未完善')
					return false
				}
				if (this.userType === 4) {
					const { enterpriseNumber } = this.$refs.stepThreeRef.formData
					this.$refs.stepFiveRef.params.enterpriseNumber = enterpriseNumber
					this.$refs.stepFiveRef.setArchivesIdentityRule()
				}
			} else if (this.step === 4) {
				const flag = await this.$refs.stepFourRef.validateForm()
				if (!flag) {
					this.$message.error('开票信息未完善')
					return false
				}
			} else if (this.step === 5) {
				const flag = await this.$refs.stepFiveRef.validateForm()
				if (!flag) {
					this.$message.error('新表卡信息未完善')
					return false
				}
			}
			return true
		},
		// 清空所有步骤数据
		resetAllData() {
			this.$refs.stepTwoRef.handleReset()
			this.$refs.stepThreeRef.handleReset()
			this.$refs.stepFourRef.handleReset()
			this.$refs.stepFiveRef.handleReset()
		},
		async nextStep() {
			if (this.step < this.stepList.length) {
				const isValid = await this.validateStep()
				if (isValid) {
					this.step++
				}
			}
		},
		prevStep() {
			if (this.step > 1) {
				this.step--
			}
		},
		async handleSubmit() {
			const newData = Object.assign({}, ...Object.values(this.detailData))
			if (newData.arrearsTotalAmount) {
				this.$message.error('当前表卡存在欠费，无法转化')
				return
			}
			const isValid = await this.validateStep()
			if (!isValid) {
				this.$message.error('表单信息未完善')
				return
			}
			const bookInfo = this.$refs.stepTwoRef.currentBookInfo
			const userInfo = this.$refs.stepThreeRef.formData
			const invoiceInfo = this.$refs.stepFourRef.formData
			const newInfo = this.$refs.stepFiveRef.formData
			const otherContactPhone = userInfo.mobileList ? userInfo.mobileList.join(',') : ''
			const purchaseContractUrl =
				userInfo.purchaseContractUrl && userInfo.purchaseContractUrl.length
					? JSON.stringify(
							userInfo.purchaseContractUrl.map(item => {
								return {
									name: item.name,
									url: item.url,
								}
							}),
					  )
					: ''
			const businessLicenseUrl =
				userInfo.businessLicenseUrl && userInfo.businessLicenseUrl.length
					? JSON.stringify(
							userInfo.businessLicenseUrl.map(item => {
								return {
									name: item.name,
									url: item.url,
								}
							}),
					  )
					: ''
			const residentParams = {
				orgCode: bookInfo.orgCode,
				archives: {
					archivesId: this.$route.query.archivesId,
					bookId: bookInfo.bookId,
					contractNum: userInfo.contractNum,
					households: userInfo.households,
					resiPopulation: userInfo.resiPopulation,
					propertyOwner: userInfo.propertyOwner,
					purchaseContractUrl,
					archivesIdentity: newInfo.archivesIdentity,
				},
				user: {
					zipCode: userInfo.zipCode,
					email: userInfo.email,
					chargingMethod: userInfo.chargingMethod,
					mailingAddress: userInfo.mailingAddress,
					userName: userInfo.userName,
					userSubType: userInfo.userSubType,
					userType: 3,
					userMobile: userInfo.userMobile,
					nameUsedBefore: userInfo.nameUsedBefore,
					contactPeople: userInfo.contactPeople,
					contactPhone: userInfo.contactPhone,
					certificateNo: userInfo.certificateNo,
					certificateType: userInfo.certificateType,
					otherCertificateNo: userInfo.otherCertificateNo,
					userId: userInfo.userId,
					otherContactPhone,
					taxpayerIdentity: invoiceInfo.taxpayerIdentity,
					openBank: invoiceInfo.openBank,
					bankAccount: invoiceInfo.bankAccount,
					buyerName: invoiceInfo.buyerName,
				},
				price: {
					priceId: newInfo.priceId,
				},
			}
			const companyParams = {
				orgCode: bookInfo.orgCode,
				archives: {
					archivesId: this.$route.query.archivesId,
					bookId: bookInfo.bookId,
					businessLicenseUrl,
					purchaseContractUrl,
					archivesIdentity: newInfo.archivesIdentity,
					contractNum: userInfo.contractNum,
				},
				user: {
					userType: 4,
					userName: userInfo.userName,
					enterpriseNumber: userInfo.enterpriseNumber,
					contactPeople: userInfo.contactPeople,
					userMobile: userInfo.userMobile,
					contactPhone: userInfo.contactPhone,
					userSubType: userInfo.userSubType,
					chargingMethod: userInfo.chargingMethod,
					collectionAccountId: userInfo.collectionAccountId,
					otherContactPhone,
					invoiceType: invoiceInfo.invoiceType,
					taxpayerIdentity: invoiceInfo.taxpayerIdentity,
					openBank: invoiceInfo.openBank,
					bankAccount: invoiceInfo.bankAccount,
					buyerName: invoiceInfo.buyerName,
					zipCode: userInfo.zipCode,
					email: userInfo.email,
					mailingAddress: userInfo.mailingAddress,
				},
				price: {
					priceId: newInfo.priceId,
				},
			}

			const params = this.userType === 3 ? residentParams : companyParams

			try {
				const apiMethods = {
					transfer: apiArchivesTransferTablecard,
					transfer2: apiArchivesTransferTablecard2,
				}
				await apiMethods[this.$route.query.code](params)
				this.$message.success('转化成功')
				const path =
					this.userType === 3 ? '/meterManage/residentMeterManage' : '/meterManage/companyMeterManage'
				this.$store.dispatch('tagsView/delView', this.$route).then(() => {
					this.$router.push({
						path,
					})
				})
			} catch (error) {
				console.log(error)
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.container {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
}
.base-info {
	margin-bottom: 12px;
	height: 185px;
}
.border-box {
	height: 12px;
	background-color: #eceff8;
}
.operate {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;

	.steps-container {
		margin-bottom: 35px;
		width: 1000px;
	}
}
.empty {
	justify-content: center;
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	img {
		margin-bottom: 20px;
	}
	span {
		font-family: Source Han Sans CN;
		font-size: 16px;
		font-weight: 350;
	}
}
.button-group {
	width: 100%;
	display: flex;
	justify-content: center;
	padding-top: 10px;
	padding-bottom: 10px;
	border-top: 1px solid #eef0f3;
}
</style>
