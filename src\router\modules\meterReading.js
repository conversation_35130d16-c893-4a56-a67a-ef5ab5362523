import Layout from '@/layout'

export default [
	{
		path: '/meterReading',
		name: 'MeterReading',
		component: Layout,
		redirect: '/meterReading/recordView',
		meta: {
			title: '抄表',
			icon: 'icon-cis_yj_chaobiao',
			permissions: [
				'plan-collection_meterReadingBook_getBookList',
				'plan-collection_meterReadingBook_addBook',
				'plan-collection_meterReadingBook_updateBook',
				'plan-collection_report_bookList_export_excel',
				'plan-collection_report_bookArchivesList_export_excel',
				'plan-collection_meterReadingTask_createFirstTask',
				'plan-collection_meterReadingBook_getBookArchivesList2',
				'plan-collection_meterReadingBook_bookArchivesMove',
				'plan-collection_meterReadingBook_bookArchivesDisplace',
				'meterReadingBook_transfer_meterCardOpsPage',
				'meterReadingBook_transfer_register',
				'meterReadingBook_transfer_queryTransferPage',
				'meterReadingBook_transfer_export_excel',
				'meterReadingBook_record_preview',
				'plan-collection_meterReadingTask_getTaskList2',
				'plan-collection_meterReadingTask_autoReviewByTask2',
				'plan-collection_meterReadingTask_getRecordList2',
				'plan-collection_meterReadingTask_updateMeterReadingRecord',
				'plan-collection_meterReadingTask_autoReviewByRecord',
				'plan-collection_meterReadingTask_importMeterReadingRecordExcel',
				'plan-collection_meterReadingTask_getRecordList',
				'plan-collection_meterReadingBook_getBookArchivesList',
				'plan-collection_meterReadingBook_updateBook2',
				'plan-collection_meterReadingBook_updateArchivesSeq',
				'plan-collection_meterReadingBook_meterPrint',
				'plan-collection_meterReadingBook_getBookModifyRecordList',
				'plan-collection_meterReadingTask_getTaskList',
				'plan-collection_meterReadingTask_autoReviewByTask',
				'plan-collection_meterReadingReview_getReviewList',
				'plan-collection_meterReadingReview_getReviewDetailList',
				'plan-collection_report_reviewList_export_excel1',
				'plan-collection_meterReadingReview_getReviewDetailList2',
				'plan-collection_report_reviewList_export_excel2',
				'plan-collection_meterReadingReview_batchReviewPass1',
				'plan-collection_meterReadingReview_batchReviewReject1',
				'plan-collection_meterReadingReview_reviewPass',
				'plan-collection_meterReadingReview_reviewReject',
				'plan-collection_meterReadingTask_updateMeterReadingRecord2',
				'plan-collection_meterReadingReview_getReviewDetailList3',
				'plan-collection_meterReadingReview_waterSpilt',
				'plan-collection_meterReadingReview_updateWaterSpilt',
				'plan-collection_meterReadingReview_batchReviewPass2',
				'plan-collection_meterReadingReview_reviewPass2',
				'plan-collection_meterReadingReview_reviewReject2',
				'plan-collection_meterReadingTask_updateMeterReadingRecord3',
				'plan-collection_meterReadingReview_getReviewDetailList4',
				'billing_bill_create',
				'billing_bill_meterTaskCreateBill',
				'plan-collection_report_reviewList_export_excel4',
				'plan-collection_meterReadingReview_v2_getReviewDetailList',
				'plan-collection_meterReadingReview_v2_getReviewDetailList2',
				'plan-collection_meterReadingReview_asyncBatchReviewPassAndCreateBill',
				'plan-collection_meterReadingReview_v2_batchReviewReject',
				'plan-collection_meterReadingReview_reviewPass3',
				'plan-collection_meterReadingReview_reviewReject3',
				'plan-collection_meterReadingTask_updateMeterReadingRecord4',
				'plan-collection_meterReadingReview_v2_getReviewDetailList3',
				'plan-collection_meterReadingReview_v2_batchCheckWater',
				'plan-collection_meterReadingReview_updateWaterSpilt2',
				'plan-collection_meterReadingReview_asyncBatchReviewPassAndCreateBill2',
				'plan-collection_meterReadingReview_v2_batchReviewReject2',
				'plan-collection_meterReadingReview_reviewReject4',
				'plan-collection_meterReadingTask_updateMeterReadingRecord5',
				'plan-collection_meterReadingReview_v2_getReviewDetailList4',
				'plan-collection_meterReadingReview_asyncBatchReviewPassAndCreateBill5',
			],
		},
		children: [
			{
				path: 'recordManage',
				name: 'RecordManage',
				component: () => import('@/views/meter-reading/record-manage/index.vue'),
				meta: {
					title: '册本管理',
					icon: 'icon-cis_ej_cebenguanli',
					keepAlive: true,
					permissions: [
						'plan-collection_meterReadingBook_getBookList',
						'plan-collection_meterReadingBook_addBook',
						'plan-collection_meterReadingBook_updateBook',
						'plan-collection_report_bookList_export_excel',
						'plan-collection_report_bookArchivesList_export_excel',
						'plan-collection_meterReadingTask_createFirstTask',

						'meterReadingBook_transfer_meterCardOpsPage',
						'plan-collection_meterReadingBook_getBookArchivesList2',
					],
				},
			},
			{
				path: 'recordView',
				name: 'RecordView',
				component: () => import('@/views/meter-reading/record-view/index.vue'),
				meta: {
					title: '册本视图',
					icon: 'icon-erji-danganguanli',
					keepAlive: true,
					permissions: [
						'plan-collection_meterReadingBook_getBookArchivesList',
						'plan-collection_meterReadingBook_updateBook2',
						'plan-collection_meterReadingBook_updateArchivesSeq',
						'plan-collection_meterReadingBook_meterPrint',
						'cpm_archives_detail-list',
						'plan-collection_meterReadingBook_getBookModifyRecordList',
						'plan-collection_meterReadingTask_getTaskList',
						'plan-collection_meterReadingTask_autoReviewByTask',

						'plan-collection_meterReadingTask_getRecordList2',
						'meterReadingBook_transfer_meterCardOpsPage',
						'plan-collection_meterReadingTask_getRecordList',
					],
				},
				hidden: true,
			},
			{
				path: 'meterReadingTaskManage',
				name: 'MeterReadingTaskManage',
				component: () => import('@/views/meter-reading/meter-reading-task-manage/index.vue'),
				meta: {
					title: '抄表任务管理',
					icon: 'icon-cis_ej_chaobiaorenwu',
					keepAlive: true,
					permissions: [
						'plan-collection_meterReadingTask_getTaskList2',
						'plan-collection_meterReadingTask_autoReviewByTask2',

						'plan-collection_meterReadingTask_getRecordList',
						'plan-collection_meterReadingTask_getRecordList2',
					],
				},
			},
			{
				path: 'meterReadingRecords',
				name: 'MeterReadingRecords',
				component: () => import('@/views/meter-reading/meter-reading-records/index.vue'),
				meta: {
					title: '抄表记录',
					icon: 'icon-cis_ej_chaobiaojilu',
					keepAlive: true,
					permissions: ['plan-collection_meterReadingTask_getRecordList'],
				},
			},
			{
				path: 'meterReadingVerification',
				name: 'MeterReadingVerification',
				component: () => import('@/views/meter-reading/meter-reading-verification/index.vue'),
				meta: {
					title: '抄表复核',
					icon: 'icon-cis_ej_chaobiaofuhe',
					keepAlive: true,
					permissions: ['plan-collection_meterReadingReview_getReviewList'],
				},
			},
			{
				path: 'meterReadingVerificationDetail',
				name: 'MeterReadingVerificationDetail',
				component: () => import('@/views/meter-reading/meter-reading-verification-detail/index.vue'),
				meta: {
					title: '抄表复核详情',
					icon: 'icon-erji-danganguanli',
					keepAlive: true,
					permissions: [
						'plan-collection_meterReadingReview_getReviewDetailList',
						'plan-collection_report_reviewList_export_excel1',
						'plan-collection_meterReadingReview_getReviewDetailList2',
						'plan-collection_report_reviewList_export_excel2',
						'plan-collection_meterReadingReview_batchReviewPass1',
						'plan-collection_meterReadingReview_batchReviewReject1',
						'plan-collection_meterReadingReview_reviewPass',
						'plan-collection_meterReadingReview_reviewReject',
						'plan-collection_meterReadingTask_updateMeterReadingRecord2',
						'plan-collection_meterReadingReview_getReviewDetailList3',
						'plan-collection_meterReadingReview_waterSpilt',
						'plan-collection_meterReadingReview_updateWaterSpilt',
						'plan-collection_meterReadingReview_batchReviewPass2',
						'plan-collection_meterReadingReview_reviewPass2',
						'plan-collection_meterReadingReview_reviewReject2',
						'plan-collection_meterReadingTask_updateMeterReadingRecord3',
						'plan-collection_meterReadingReview_getReviewDetailList4',
						'billing_bill_create',
						'billing_bill_meterTaskCreateBill',
						'plan-collection_report_reviewList_export_excel4',
					],
				},
				hidden: true,
			},
			{
				path: 'meterReadingBatchVerification',
				name: 'MeterReadingBatchVerification',
				component: () => import('@/views/meter-reading/meter-reading-batch-verification/index.vue'),
				meta: {
					title: '批量复核',
					icon: 'icon-cis_ej_chaobiaofuhe',
					keepAlive: true,
					permissions: [
						'plan-collection_meterReadingReview_v2_getReviewDetailList',
						'plan-collection_meterReadingReview_v2_getReviewDetailList2',
						'plan-collection_meterReadingReview_asyncBatchReviewPassAndCreateBill',
						'plan-collection_meterReadingReview_v2_batchReviewReject',
						'plan-collection_meterReadingReview_reviewPass3',
						'plan-collection_meterReadingReview_reviewReject3',
						'plan-collection_meterReadingTask_updateMeterReadingRecord4',
						'plan-collection_meterReadingReview_v2_getReviewDetailList3',
						'plan-collection_meterReadingReview_v2_batchCheckWater',
						'plan-collection_meterReadingReview_updateWaterSpilt2',
						'plan-collection_meterReadingReview_asyncBatchReviewPassAndCreateBill2',
						'plan-collection_meterReadingReview_v2_batchReviewReject2',
						'plan-collection_meterReadingReview_reviewReject4',
						'plan-collection_meterReadingTask_updateMeterReadingRecord5',
						'plan-collection_meterReadingReview_v2_getReviewDetailList4',
						'plan-collection_meterReadingReview_asyncBatchReviewPassAndCreateBill5',
					],
				},
			},
			{
				path: 'meterReadingInput',
				name: 'MeterReadingInput',
				component: () => import('@/views/meter-reading/meter-reading-input/index.vue'),
				meta: {
					title: '抄表录入',
					icon: 'icon-erji-danganguanli',
					keepAlive: true,
					permissions: [
						'plan-collection_meterReadingTask_getRecordList2',
						'plan-collection_meterReadingTask_updateMeterReadingRecord',
						'plan-collection_meterReadingTask_autoReviewByRecord',
						'plan-collection_meterReadingTask_importMeterReadingRecordExcel',
					],
				},
				hidden: true,
			},
			{
				path: 'meterReadingCollect',
				name: 'MeterReadingCollect',
				component: () => import('@/views/meter-reading/meter-reading-collect/index.vue'),
				meta: {
					title: '追收抄表',
					icon: 'icon-erji-danganguanli',
					keepAlive: true,
					permissions: ['plan-collection_meterReadingTask_createMeterReadingCollection'],
				},
				hidden: true,
			},
			{
				path: 'recordAdjust',
				name: 'RecordAdjust',
				hidden: false,
				component: () => import('@/views/meter-reading/meter-reading-adjust/index.vue'),
				meta: {
					title: '册本调整',
					icon: 'icon-cis_ej_cebentiaozheng',
					keepAlive: true,
					permissions: [
						'plan-collection_meterReadingBook_getBookArchivesList2',
						'plan-collection_meterReadingBook_bookArchivesMove',
						'plan-collection_meterReadingBook_bookArchivesDisplace',
					],
				},
			},
			{
				path: 'meterReadingTransfer',
				name: 'MeterReadingTransfer',
				hidden: false,
				component: () => import('@/views/meter-reading/meter-reading-transfer/index.vue'),
				meta: {
					title: '册本移交',
					icon: 'icon-cis_ej_cebenyijiao',
					keepAlive: true,
					permissions: ['meterReadingBook_transfer_meterCardOpsPage', 'meterReadingBook_transfer_register'],
				},
			},
			{
				path: 'meterReadingTransferRecord',
				name: 'MeterReadingTransferRecord',
				hidden: false,
				component: () => import('@/views/meter-reading/meter-reading-transfer-record/index.vue'),
				meta: {
					title: '册本移交记录',
					icon: 'icon-cis_ej_cebenyijiaojilu',
					keepAlive: true,
					permissions: [
						'meterReadingBook_transfer_queryTransferPage',
						'meterReadingBook_transfer_export_excel',
						'meterReadingBook_record_preview',
					],
				},
			},
		],
	},
]
