<template>
	<gc-el-dialog :show="isShow" :title="`${typeText}员工`" width="600px" @close="handleClose">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<template v-slot:orgCode>
				<el-select v-model="formData.orgCode" @change="handleOrgCodeChange">
					<el-option
						v-for="item in orgList"
						:key="item.orgCode"
						:label="item.orgName"
						:value="item.orgCode"
					></el-option>
				</el-select>
			</template>
			<template v-slot:departmentCode>
				<el-cascader
					style="width: 100%"
					v-model="formData.departmentCode"
					filterable
					:showAllLevels="false"
					:options="departmentTreeList"
					:props="{ emitPath: false }"
				></el-cascader>
			</template>
		</GcFormSimple>
		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { staffStatusOptions } from '@/consts/optionList.js'
import { ruleRequired, ruleMaxLength, RULE_PHONE } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { addWorkStaff, updateWorkStaff } from '@/api/basicConfig.api.js'
import { apiGetDepartmentTree, apiGetBusinessHallCompanyMap } from '@/api/organizationManage.api.js'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		editType: {
			type: String,
			default: 'add',
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		typeText() {
			return this.editType === 'add' ? '新增' : '编辑'
		},
		departmentTreeList() {
			const { formData, orgCodeMap } = this
			const { orgCode } = formData || {}
			const tree = orgCodeMap[orgCode]
			return tree ? [tree] : []
		},
	},
	watch: {
		isShow(flag) {
			if (flag) {
				this.getBusinessHallCompanyMap()
				this._getOrgStruData()
			}
		},
		// departmentTreeList: {
		// 	handler() {
		// 		const cache = this.formData.departmentCode || ''
		// 		this.formData.departmentCode = ''
		// 		debugger
		// 		this.$nextTick(() => {
		// 			this.formData.departmentCode = cache
		// 		})
		// 	},
		// },
	},

	data() {
		return {
			formData: {
				staffId: '',
				orgCode: '',
				staffName: '',
				staffPhone: '',
				status: '',
				staffType: '',
				departmentCode: '',
			},
			formItems: [
				{
					type: 'slot',
					label: '营业分公司',
					prop: 'orgCode',
					slotName: 'orgCode',
				},
				{
					type: 'slot',
					label: '所属部门',
					prop: 'departmentCode',
					slotName: 'departmentCode',
					attrs: {
						filterable: true,
						'show-all-levels': false,
						options: [],
						col: 12,
						props: {
							emitPath: false,
						},
					},
				},
				{
					type: 'el-input',
					label: '员工名称',
					prop: 'staffName',
				},
				{
					type: 'el-input',
					label: '电话',
					prop: 'staffPhone',
				},
				{
					type: 'el-select',
					label: '状态',
					prop: 'status',
					options: staffStatusOptions,
				},
			],
			formAttrs: {
				labelPosition: 'top',
				rules: {
					orgCode: [ruleRequired('必填')],
					staffName: [ruleRequired('必填'), ruleMaxLength(16, '员工名称')],
					staffPhone: [RULE_PHONE],
					status: [ruleRequired('必填')],
					departmentCode: [ruleRequired('必填')],
				},
			},
			treeList: [],
			organizaTreeMap: {},
			orgCodeMap: {},
			orgList: [],
		}
	},
	methods: {
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				const formParams = trimParams(removeNullParams(this.formData))
				if (this.editType === 'add') {
					await addWorkStaff(formParams)
				} else {
					await updateWorkStaff(formParams)
				}
				this.$message.success(`${this.typeText}成功`)
				this.$emit('success')
				this.isShow = false
			}
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.isShow = false
		},
		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
		},
		formatOrganizeTreeList(data, map = {}, orgCodeMap = {}) {
			data = data || []
			return data.map(depData => {
				let {
					tenant_organization,
					department,
					children,
					department_code,
					department_name,
					parent_org_code,
				} = depData
				tenant_organization = tenant_organization || {}
				department = department || []
				children = children || []

				const value =
					department_code === undefined
						? `${tenant_organization.uid}_${tenant_organization.org_code}`
						: department_code
				const label = department_name === undefined ? tenant_organization.name : department_name
				const parentOrgCode = parent_org_code === undefined ? '' : parent_org_code
				const formatedChildList = this.formatOrganizeTreeList([...department, ...children], map, orgCodeMap)

				const item = {
					value,
					label,
					parentOrgCode,
					children: department_code === undefined ? formatedChildList : null,
				}

				map[value] = item

				if (tenant_organization.org_code) {
					orgCodeMap[tenant_organization.org_code] = item
				}

				return item
			})
		},
		// 获取所属部门
		async _getOrgStruData() {
			try {
				const result = await apiGetDepartmentTree({})
				const organizaTreeMap = {}
				const orgCodeMap = {}
				const treeList = this.formatOrganizeTreeList(result, organizaTreeMap, orgCodeMap)
				this.organizaTreeMap = organizaTreeMap
				this.treeList = treeList
				this.orgCodeMap = orgCodeMap
			} catch (e) {
				console.log(e)
			}
		},
		getBusinessHallCompanyMap() {
			apiGetBusinessHallCompanyMap({}).then(result => {
				this.orgList = result || []
			})
		},
		handleOrgCodeChange() {
			this.formData.departmentCode = ''
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
</style>
