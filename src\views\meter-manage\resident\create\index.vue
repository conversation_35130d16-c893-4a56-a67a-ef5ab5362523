<template>
	<div class="wrapper" v-loading.fullscreen.lock="loading">
		<div class="left-wrapper">
			<div class="left-title">档案简目录</div>
			<GcTab :tabList="tabList" @changeTab="v => (activeTab = v)" :defaultTab="activeTab" />
		</div>
		<div class="right-wrapper">
			<RecordChoose
				ref="recordChooseRef"
				v-show="activeTab == 'recordChoose'"
				@changeTab="v => (activeTab = v)"
				@getValid="getValid"
				:detailData="detailData"
			/>
			<AddressChoose
				ref="addressChooseRef"
				v-show="activeTab == 'addressChoose'"
				@changeTab="v => (activeTab = v)"
				@getValid="getValid"
				:detailData="detailData"
			/>
			<UserInfo
				ref="userInfoRef"
				v-show="activeTab == 'userInfo'"
				@changeTab="v => (activeTab = v)"
				@getValid="getValid"
				:detailData="detailData"
			/>
			<MeterInfo
				ref="meterInfoRef"
				v-show="activeTab == 'meterInfo'"
				@changeTab="v => (activeTab = v)"
				@getValid="getValid"
				:detailData="detailData"
			/>
			<RecordsInfo
				ref="recordsInfoRef"
				v-show="activeTab == 'recordsInfo'"
				@changeTab="v => (activeTab = v)"
				@getValid="getValid"
				:detailData="detailData"
			/>
		</div>
		<div class="button-group">
			<el-button class="btn-preview" @click="handleCancel">取消</el-button>
			<el-button
				v-has="['cpm_archives_add', 'cpm_archives_update']"
				class="btn-create"
				type="primary"
				@click="handleSubmit"
			>
				{{ isModify ? '保存修改' : '确定建档' }}
			</el-button>
		</div>
	</div>
</template>

<script>
import { removeNullParams, trimParams } from '@/utils/index.js'
import { apiArchives, apiUpdateArchives, apiGetArchivesDetail } from '@/api/meterManage.api'
import RecordChoose from './record-choose'
import AddressChoose from './address-choose'
import MeterInfo from './meter-info'
import UserInfo from './user-info'
import RecordsInfo from './records-info'
export default {
	name: '',
	components: {
		RecordChoose,
		AddressChoose,
		MeterInfo,
		UserInfo,
		RecordsInfo,
	},
	data() {
		return {
			tabList: [
				{
					label: '册本选择',
					value: 'recordChoose',
					status: 1,
					disabled: false,
					tip: '待选择',
				},
				{
					label: '地址选择',
					value: 'addressChoose',
					status: 1,
					disabled: true,
					tip: '待完善',
				},
				{
					label: '用户信息',
					value: 'userInfo',
					status: 1,
					disabled: true,
					tip: '待完善',
				},
				{
					label: '表具信息',
					value: 'meterInfo',
					status: 1,
					disabled: true,
					tip: '待完善',
				},
				{
					label: '档案信息',
					value: 'recordsInfo',
					status: 1,
					disabled: true,
					tip: '待完善',
				},
			],
			activeTab: '',
			detailData: {},
			loading: false,
		}
	},
	computed: {
		isModify() {
			return this.$route.query.archivesId
		},
	},
	async activated() {
		const { archivesId } = this.$route.query
		if (archivesId) {
			await this._apiGetArchivesDetail({ archivesId })
		}
		this.$refs.recordChooseRef._getAlleyMap()
		this.$refs.addressChooseRef.updateAddress()
		this.$refs.meterInfoRef._apiGetMeterType()
		this.$refs.recordsInfoRef.updatePrice()
	},
	methods: {
		getValid(key, flag) {
			const obj = this.tabList.find(item => item.value === key)
			if (obj) {
				obj.status = flag ? 2 : 1
			}
			if (key === 'recordChoose') {
				this.handleRecordChoose(flag)
			}
		},
		// 册本选择
		handleRecordChoose(flag) {
			// 册本未选择时,其他tab不可编辑, flag为true时,其他tab可编辑, flag为false时,其他tab不可编辑
			this.tabList.forEach(item => {
				if (item.value !== 'recordChoose') {
					item.disabled = flag ? false : true
				}
			})
			// 已选册本，回显信息
			if (flag) {
				const { bookNo = '', alleyCode, bookId, orgCode } = this.$refs.recordChooseRef.currentBookInfo || {}
				// 表具信息: 回显表册编号
				this.$refs.meterInfoRef.formData.bookNo = bookNo
				this.$refs.meterInfoRef.formData.bookId = bookId
				this.$refs.meterInfoRef.formData.orgCode = orgCode
				// 档案信息：设置表卡编号规则
				this.$refs.recordsInfoRef.setArchivesIdentityRule(alleyCode)
				// 档案信息：设置获取表卡编号接口参数
				this.$refs.recordsInfoRef.setArchivesIdentityParams({
					bookId,
					orgCode,
				})
			}
		},
		handleCancel() {
			this.$store.dispatch('tagsView/delView', this.$route).then(tags => {
				const { fullPath } = tags.slice(-1)[0]
				this.$router.push(fullPath || '/')
			})
		},
		handleSubmit() {
			const obj = this.tabList.find(item => item.status == 1)
			if (obj) {
				this.$message.error(`${obj.value == 'recordChoose' ? '未选择册本' : obj.label + obj.tip}`)
				this.activeTab = obj.value
			} else {
				// 册本选择
				const bookObj = this.$refs.recordChooseRef.currentBookInfo
				console.log('册本信息', bookObj)
				// 地址选择
				const { addressAreaCode, addressFullName, formData } = this.$refs.addressChooseRef
				const addressParams = trimParams(removeNullParams(formData))
				addressParams['addressAreaCode'] = addressAreaCode
				addressParams['addressFullName'] = addressFullName
				console.log('地址选择', addressParams)

				// 用户信息
				const userInfoParams = trimParams(removeNullParams(this.$refs.userInfoRef.formData))
				const otherContactPhone = userInfoParams.mobileList ? userInfoParams.mobileList.join(',') : ''
				const purchaseContractUrl =
					userInfoParams.purchaseContractUrl && userInfoParams.purchaseContractUrl.length
						? JSON.stringify(
								userInfoParams.purchaseContractUrl.map(item => {
									return {
										name: item.name,
										url: item.url,
									}
								}),
						  )
						: ''
				userInfoParams['userType'] = 3 // 3居民
				console.log('用户信息', userInfoParams)

				// 表具信息
				const meterInfoParams = trimParams(removeNullParams(this.$refs.meterInfoRef.formData))
				console.log('表具信息', meterInfoParams)

				// 档案信息
				const recordsInfoParams = trimParams(removeNullParams(this.$refs.recordsInfoRef.formData))
				console.log('档案信息', recordsInfoParams)

				const params = {
					orgCode: bookObj.orgCode,
					archives: {
						bookId: bookObj.bookId,
						contractNum: userInfoParams.contractNum,
						households: userInfoParams.households,
						resiPopulation: userInfoParams.resiPopulation,
						propertyOwner: userInfoParams.propertyOwner,
						purchaseContractUrl,
						archivesIdentity: recordsInfoParams.archivesIdentity,
						oldArchivesIdentity: recordsInfoParams.oldArchivesIdentity,
						summaryArchives: recordsInfoParams.summaryArchives,
						archivesMeterType: recordsInfoParams.archivesMeterType,
						accountNumber: recordsInfoParams.accountNumber,
						recordSeq: meterInfoParams.recordSeq,
						remark: meterInfoParams.remark,
					},
					user: {
						userName: userInfoParams.userName,
						userSubType: userInfoParams.userSubType,
						userType: userInfoParams.userType,
						userMobile: userInfoParams.userMobile,
						nameUsedBefore: userInfoParams.nameUsedBefore,
						contactPeople: userInfoParams.contactPeople,
						contactPhone: userInfoParams.contactPhone,
						certificateNo: userInfoParams.certificateNo,
						certificateType: userInfoParams.certificateType,
						otherCertificateNo: userInfoParams.otherCertificateNo,
						zipCode: userInfoParams.zipCode,
						email: userInfoParams.email,
						chargingMethod: userInfoParams.chargingMethod,
						mailingAddress: userInfoParams.mailingAddress,
						otherContactPhone,
						taxpayerIdentity: userInfoParams.taxpayerIdentity,
						openBank: userInfoParams.openBank,
						bankAccount: userInfoParams.bankAccount,
						buyerName: userInfoParams.buyerName,
					},
					address: {
						tapWaterNo: addressParams.tapWaterNo,
						regionCode: addressParams.regionCode,
						addressAreaCode: addressParams.addressAreaCode,
						addressFullName: addressParams.addressFullName + addressParams.addressName,
						addressName: addressParams.addressName,
						houseYear: addressParams.houseYear,
						floorNum: addressParams.floorNum,
						pressureZone: addressParams.pressureZone,
						gisCode: addressParams.gisCode,
						pipeNetworkCode: addressParams.pipeNetworkCode,
					},
					meter: meterInfoParams,
					price: {
						priceId: recordsInfoParams.priceId,
					},
				}
				if (userInfoParams.userId) {
					params.user['userId'] = userInfoParams.userId
				}
				// 更新
				if (this.isModify) {
					const { archivesId, addressId, meterId, userId } = this.detailData
					params.archives['archivesId'] = archivesId
					params.address['addressId'] = addressId
					params.meter['meterId'] = meterId
					params.user['userId'] = userId
					this._apiUpdateArchives(params)
				} else {
					this._apiArchives(params)
				}
			}
		},
		async _apiArchives(params) {
			try {
				this.loading = true
				const { archivesId } = await apiArchives(params)
				this.$store.dispatch('tagsView/delView', this.$route).then(() => {
					this.$router.push({
						path: '/meterManage/residentMeterView',
						query: {
							archivesId,
						},
					})
				})
			} catch (error) {
				console.error(error)
			} finally {
				this.loading = false
			}
		},
		async _apiGetArchivesDetail(params) {
			const res = await apiGetArchivesDetail(params)
			this.detailData = Object.assign({}, ...Object.values(res))
		},
		async _apiUpdateArchives(params) {
			try {
				this.loading = true
				await apiUpdateArchives(params)
				this.$store.dispatch('tagsView/delView', this.$route).then(() => {
					this.$router.push({
						path: '/meterManage/residentMeterView',
						query: {
							archivesId: this.$route.query.archivesId,
						},
					})
				})
			} catch (error) {
				console.error(error)
			} finally {
				this.loading = false
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	display: flex;
	flex-wrap: wrap;
	height: 100%;
}
.left-wrapper {
	position: relative;
	padding: 20px;
	flex-grow: 0;
	flex-shrink: 0;
	width: 240px;
	height: calc(100% - 40px);
	background-color: #fff;
	.left-title {
		height: 48px;
		line-height: 48px;
		color: #000000;
		font-family: Source Han Sans CN;
		font-size: 16px;
		font-weight: 500;
	}
}
.left-wrapper:after {
	position: absolute;
	top: 20px;
	bottom: 20px;
	right: 0;
	content: '';
	display: block;
	clear: both;
	width: 1px;
	border-right: 1px dashed #eef0f3;
}
.right-wrapper {
	width: 0;
	flex: 1;
	padding: 20px;
	height: calc(100% - 40px);
	background-color: #fff;
}
.button-group {
	width: 100%;
	height: 40px;
	.btn-create,
	.btn-preview {
		margin-top: 16px;
		border-radius: 4px;
		height: 32px;
	}
	.btn-create {
		width: 216px;
	}
	.btn-preview {
		width: 110px;
		border: 1px solid #2f87fe;
		color: #2f87fe;
	}
}
::v-deep .el-col-24:last-child {
	.el-form-item {
		margin-bottom: 0;
	}
}
</style>
