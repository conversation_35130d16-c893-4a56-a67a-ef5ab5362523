<template>
	<div class="flex">
		<CardTotal :archives="archives" />
		<div class="water-warning">
			<div class="title-wrapper">
				<span>当月（收费月）用水大量提示</span>
				<span>总件数：{{ pageData.total }}个</span>
			</div>
			<div class="table-container">
				<GcTable
					:loading="loading"
					:columns="columns"
					:table-data="tableData"
					showPage
					:page-size="pageData.size"
					:total="pageData.total"
					:current-page="pageData.current"
					@current-page-change="changePage"
				/>
			</div>
		</div>
	</div>
</template>

<script>
import CardTotal from './CardTotal.vue'
import { apiGetObtainUserBigAmountChart } from '@/api/home.api'
export default {
	name: 'WaterWarning',
	components: { CardTotal },
	props: {
		orgCode: {
			type: String,
			default: '',
		},
	},
	watch: {
		orgCode(v) {
			if (v) {
				if (!this.$has('cpm_home_charts_obtainUserBigAmountChart')) {
					return
				}
				this.getList()
			}
		},
	},
	data() {
		return {
			columns: [
				{
					key: 'archivesIdentity',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'userName',
					name: '用户名称',
					tooltip: true,
				},
				{
					key: 'address',
					name: '地址',
					tooltip: true,
				},
				{
					key: 'yearAmount',
					name: '年累计水量(m³)',
					tooltip: true,
				},
				{
					key: 'monthWaterAmt',
					name: '当月水费(￥)',
					tooltip: true,
				},
				{
					key: 'wsf',
					name: '污水处理费(￥)',
					tooltip: true,
				},
				{
					key: 'monthTotalAmt',
					name: '合计费用(￥)',
					tooltip: true,
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			loading: false,
			archives: 0,
		}
	},

	methods: {
		async getList() {
			try {
				this.tableData = []
				const { archives, pages } = await apiGetObtainUserBigAmountChart({
					orgCode: this.orgCode,
					current: this.pageData.current,
					size: this.pageData.size,
				})
				const { records = [], total = 0 } = pages

				this.archives = archives
				if (Array.isArray(records) && records.length) {
					this.tableData = records
				}
				this.pageData.total = total
			} catch (error) {
				console.error(error)
			}
		},
		changePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
	},
}
</script>

<style lang="scss" scoped>
.flex {
	width: 100%;
	display: flex;
	gap: 10px;
}
.water-warning {
	display: flex;
	flex-direction: column;
	border: 1px solid #d5d8e2;
	border-radius: 8px;
	padding: 10px;
	width: calc(100% - 378px);
	height: 468px;

	.title-wrapper {
		display: flex;
		justify-content: space-between;
		margin-bottom: 10px;
		border-radius: 8px;
		padding: 12px;
		background: linear-gradient(92.14deg, rgba(151, 203, 255, 0.4) -1.61%, rgba(191, 219, 255, 0.35) 96.3%);
		font-family: Alimama ShuHeiTi;
		font-weight: 700;
		font-size: 18px;
		color: #000000;
	}
	.table-container {
		flex: 1;
		height: 0;
	}
}
</style>
