<template>
	<GcElDialog
		ref="dialog"
		:show="isShow"
		:title="title"
		width="700px"
		okText="开票"
		class="invoice-dialog"
		@close="handleClose"
		@cancel="handleClose"
		@ok="handleSubmit"
	>
		<InvoiceInfoSelect
			:is-show="isShow"
			:archivesIds="this.archivesIds"
			:queryExtraParams="queryExtraParams"
			@error="handleError"
			@select="handleInvoiceSelect"
		/>
	</GcElDialog>
</template>

<script>
import InvoiceInfoSelect from './InvoiceInfoSelect.vue'
export default {
	components: {
		InvoiceInfoSelect,
	},
	props: {
		show: {
			type: Boolean,
			default: false,
		},
		type: {
			type: String,
			default: 'single',
		},
		billList: {
			type: Array,
			default: () => [],
		},
		api: {
			type: [Function, Object],
			default: null,
		},
		queryExtraParams: {
			type: Object,
			default: () => {
				return {}
			},
		},
		submitExtraParams: {
			type: Object,
			default: () => {
				return {}
			},
		},
		// 是否需要备注地址弹框确认
		showRemarkAddress: {
			type: Boolean,
			default: true,
		},
	},
	computed: {
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},
		title() {
			const titleMap = {
				single: '开票',
				merge: '合并开票',
				batch: '批量开票',
			}
			return titleMap[this.type]
		},
		isSingle() {
			return this.type === 'single'
		},
		archivesIds() {
			if (!this.billList.length) return null
			return [...new Set(this.billList.map(item => item.archivesId))]
		},
		apiInstance() {
			if (typeof this.api === 'function') {
				return this.api
			}
			if (this.api && typeof this.api === 'object') {
				return this.api[this.type]
			}
			return null
		},
	},
	data() {
		return {
			selectedInvoiceInfo: null,
			loading: null,
		}
	},
	methods: {
		handleInvoiceSelect(invoiceInfo) {
			this.selectedInvoiceInfo = invoiceInfo
		},
		showLoading(text) {
			const targetInstance = this.$el.querySelector('.el-dialog')
			this.loading = this.$loading({
				target: targetInstance,
				lock: true,
				text: text,
				spinner: 'el-icon-loading',
				background: 'rgba(255, 255, 255, 0.5)',
			})
		},
		handleSubmit() {
			if (!this.selectedInvoiceInfo) {
				this.$message.error('请选择开票信息')
				return
			}
			this.handleRemarkAddress()
		},
		handleClose() {
			this.isShow = false
		},
		handleError(message) {
			this.$message.error(message)
			this.isShow = false
		},
		singleOpenInvoice(isRemarkAddress) {
			this.showLoading('开票中...')
			if (!this.apiInstance) {
				this.$emit('submit', {
					invoiceInfo: this.selectedInvoiceInfo,
					isRemarkAddress,
					loading: this.loading,
				})
				return
			}
			const { phoneNumber, ...rest } = this.selectedInvoiceInfo
			const data = {
				billKeyList: this.billList.map(item => {
					return {
						archivesId: item.archivesId,
						billId: item.billId,
						year: item.year,
					}
				}),
				...rest,
				userMobile: phoneNumber,
				isRemarkAddress,
				...this.submitExtraParams,
			}
			this.doOpenInvoiceSubmit(data)
		},
		mergeOrBatchOpenInvoice(isRemarkAddress) {
			this.showLoading('开票中...')
			if (!this.apiInstance) {
				this.$emit('submit', {
					isRemarkAddress,
					loading: this.loading,
				})
				return
			}
			const data = {
				billKeyList: this.billList.map(item => {
					return {
						archivesId: item.archivesId,
						billId: item.billId,
						year: item.year,
					}
				}),
				isRemarkAddress,
				...this.submitExtraParams,
			}
			this.doOpenInvoiceSubmit(data)
		},
		doOpenInvoiceSubmit(params) {
			this.apiInstance(params)
				.then(() => {
					this.$emit('success')
					this.$message.success('开票成功')
					this.isShow = false
				})
				.catch(e => {
					this.$emit('fail')
					console.error(e)
					this.$message.error(e.message || '开票失败')
				})
				.finally(() => {
					if (this.loading) this.loading.close()
				})
		},
		handleRemarkAddress() {
			if (!this.showRemarkAddress) {
				this.doOpenInvoice()
				return
			}
			this.$confirm('开票备注是否增加表卡地址?', '开票备注确认', {
				confirmButtonText: '需要',
				cancelButtonText: '否',
				distinguishCancelAndClose: true,
				beforeClose: (action, instance, done) => {
					if (action === 'confirm') {
						this.doOpenInvoice(1)
					} else if (action === 'cancel') {
						this.doOpenInvoice(0)
					}
					done()
				},
			})
		},
		doOpenInvoice(isRemarkAddress) {
			if (this.isSingle) {
				this.singleOpenInvoice(isRemarkAddress)
			} else {
				this.mergeOrBatchOpenInvoice(isRemarkAddress)
			}
		},
	},
	watch: {
		show: {
			handler(val) {
				if (val) {
					const billCount = this.billList.length
					if (!billCount) {
						this.isShow = false
						this.$message.error('请选择需要开票的账单')
						return
					}
					// todo 后续如需重复开票，增加相关参数判断
					const hasInvoiceOpened = this.billList.some(
						item => item.invoiceStatus !== 0 && item.invoiceStatus !== 2,
					)
					if (hasInvoiceOpened) {
						this.isShow = false
						const msg = billCount === 1 ? '该账单已开票，无需重复开票' : '部分账单已开票，请确认选择的账单'
						this.$message.error(msg)
						return
					}
					// 合并开票、批量开票不选择开票信息
					if (this.type === 'batch') {
						this.isShow = false
						this.handleRemarkAddress()
						return
					}
				} else {
					if (this.loading) {
						this.loading.close()
						this.loading = null
					}
				}
			},
		},
	},
}
</script>
<style lang="scss" scoped>
.invoice-dialog {
	&::v-deep {
		.el-dialog {
			max-height: calc(100vh - 200px);
		}
		.el-dialog__body {
			padding-top: 0;
		}
	}
}
</style>
