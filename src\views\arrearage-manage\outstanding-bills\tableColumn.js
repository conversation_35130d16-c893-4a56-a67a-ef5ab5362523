export function getColumn(_) {
	return [
		{
			key: 'archivesNo',
			name: '表卡编号',
			tooltip: true,
		},
		{
			key: 'billNo',
			name: '账单编号',
			tooltip: true,
		},
		{
			key: 'openBillDate',
			name: '开账日期',
			tooltip: true,
			width: 160,
		},
		{
			key: 'userName',
			name: '用户名称',
			tooltip: true,
		},
		{
			key: 'userPhone',
			name: '手机',
			tooltip: true,
		},
		{
			key: 'fullAddress',
			name: '地址',
			tooltip: true,
		},
		{
			key: 'priceCode',
			name: '价格编号',
			tooltip: true,
		},
		{
			key: 'priceVersion',
			name: '价格版本号',
			tooltip: true,
		},
		{
			key: 'priceDetail',
			name: '价格信息',
			width: 200,
			tooltip: true,
		},
		{
			key: 'priceBillItemList',
			name: '附加费信息',
			tooltip: true,
		},
		{
			key: 'waterAmount',
			name: '水量',
			tooltip: true,
		},
		{
			key: 'waterFee',
			name: '水费',
			tooltip: true,
		},
		{
			key: 'billItemFee',
			name: '附加费',
			tooltip: true,
		},
		{
			key: 'arrearsAmount',
			name: '欠费金额',
			tooltip: true,
		},
		{
			key: 'lastUrgePaymentTime',
			name: '上次催缴时间',
			tooltip: true,
			width: 160,
		},
		{
			hide: !_.$has(['cpm_urge_payment_urgeRegister', 'cpm_urge_payment_printNotice']),
			key: 'deal',
			name: '操作',
			fixed: 'right',
			width: 190,
		},
	]
}
