<template>
	<div class="complex-input-item">
		<!-- el-input、el-input-number -->
		<template v-if="field.type.indexOf('el-input') !== -1 || field.type === 'el-autocomplete'">
			<component :is="field.type" v-model.trim="innerValue" v-bind="{ placeholder: '请输入', ...field.attrs }" />
		</template>
		<!-- el-select -->
		<template v-if="field.type === 'el-select'">
			<el-select
				v-model="innerValue"
				v-bind="{
					placeholder: '请选择',
					clearable: true,
					filterable: true,
					...field.attrs,
				}"
			>
				<el-option
					v-for="option in field.options"
					:key="option.value"
					:label="option.label"
					:value="option.value"
					:disabled="option.disabled"
				></el-option>
			</el-select>
		</template>
		<!-- el-radio -->
		<template v-if="field.type === 'el-radio'">
			<el-radio-group v-model="innerValue" v-bind="field.attrs">
				<el-radio
					v-for="option in field.options"
					:key="option.value"
					:label="option.value"
					:disabled="option.disabled"
				>
					{{ option.label }}
				</el-radio>
			</el-radio-group>
		</template>
		<!-- el-date-picker -->
		<template v-if="field.type === 'el-date-picker'">
			<el-date-picker
				v-model="innerValue"
				v-bind="{ placeholder: '请选择', clearable: true, ...field.attrs }"
				:type="(field.attrs && field.attrs.type) || 'date'"
			></el-date-picker>
		</template>
		<!-- el-cascader -->
		<template v-if="field.type === 'el-cascader'">
			<el-cascader
				v-model="innerValue"
				style="width: 100%"
				:options="field.options"
				:props="field.props || {}"
				v-bind="{ placeholder: '请选择', clearable: true, ...field.attrs }"
			></el-cascader>
		</template>
	</div>
</template>
<script>
export default {
	name: 'complex-input-item',
	props: {
		field: {
			type: Object,
			default: () => {},
		},
		value: [String, Number, Boolean, Array, Object],
	},
	computed: {
		innerValue: {
			get() {
				return this.value
			},
			set(val) {
				this.$emit('input', val)
			},
		},
	},
}
</script>

<style lang="scss" scoped></style>
