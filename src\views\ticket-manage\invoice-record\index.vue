<template>
	<div class="wrapper">
		<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
			<el-form-item>
				<el-button type="primary" @click="handleSearch(false)">查询</el-button>
				<el-button @click="handleReset">重置</el-button>
			</el-form-item>
		</GcFormSimple>
		<GcTable
			:loading="loading"
			:columns="columns"
			:table-data="tableData"
			:page-size="pageData.size"
			:total="pageData.total"
			:current-page="pageData.current"
			showPage
			@current-page-change="handlePageChange"
			@dblclick="handleDbClick"
		>
			<template #invoiceType="{ row }">
				<span>{{ getfilterNameFn($store.getters.dataList.invoiceType, row.invoiceType) }}</span>
			</template>
			<template #incvoiceInfo="{ row }">
				<el-button v-show="!row.isEditing" type="text" size="medium" @click="handleCheckInvoicePdf(row)">
					查看开票
				</el-button>
			</template>
			<template #payRecord="{ row }">
				<el-button
					v-show="!row.isEditing"
					type="text"
					size="medium"
					@click="handleRecord(row, 'PaymentRecords')"
				>
					查看缴费记录
				</el-button>
			</template>
			<template #billRecord="{ row }">
				<el-button v-show="!row.isEditing" type="text" size="medium" @click="handleRecord(row, 'BillRecords')">
					关联账单记录
				</el-button>
			</template>
		</GcTable>
	</div>
</template>

<script>
import { getfilterName } from '@/utils'
import { getInvoiceRecordList, getInvoicePdf } from '@/api/ticketManage.api'

export default {
	name: 'InvoiceRecord',
	data() {
		const _this = this
		const dayStart = _this.dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss')
		const dayEnd = _this.dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
		const pickerOptions = {
			shortcuts: [
				{
					text: '今天',
					onClick(picker) {
						picker.$emit('pick', [dayStart, dayEnd])
					},
				},
				{
					text: '最近三天',
					onClick(picker) {
						const end = new Date(_this.dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'))
						const start = new Date(
							_this.dayjs().subtract(2, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
						)
						picker.$emit('pick', [start, end])
					},
				},
				{
					text: '最近一周',
					onClick(picker) {
						const end = new Date(_this.dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'))
						const start = new Date(
							_this.dayjs().subtract(6, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
						)
						picker.$emit('pick', [start, end])
					},
				},
				{
					text: '最近一个月',
					onClick(picker) {
						const end = new Date(_this.dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'))
						const start = new Date(
							_this.dayjs().subtract(29, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
						)
						picker.$emit('pick', [start, end])
					},
				},
				{
					text: '最近三个月',
					onClick(picker) {
						const end = new Date(_this.dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'))
						const start = new Date(
							_this.dayjs().subtract(90, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
						)
						picker.$emit('pick', [start, end])
					},
				},
			],
		}
		return {
			dayStart,
			dayEnd,
			formData: {
				dateRange: [dayStart, dayEnd],
				invoiceType: '',
				archivesIdentity: '',
				enterpriseNumber: '',
				collectionAgreementNumber: '',
				businessType: '',
				invoiceNo: '',
			},
			formItems: [
				{
					type: 'el-date-picker',
					label: '开票日期',
					prop: 'dateRange',
					attrs: {
						type: 'daterange',
						clearable: true,
						defaultTime: ['00:00:00', '23:59:59'],
						valueFormat: 'yyyy-MM-dd HH:mm:ss',
						startPlaceholder: '开始日期',
						endPlaceholder: '结束日期',
						pickerOptions,
					},
				},
				{
					type: 'el-select',
					label: '开票类型',
					prop: 'invoiceType',
					options:
						this.$store.getters?.dataList?.invoiceType?.map(item => {
							return {
								label: item.sortName,
								value: item.sortValue,
							}
						}) || [],
					attrs: {
						clearable: true,
						placeholder: '请选择开票类型',
					},
				},
				{
					type: 'el-input',
					label: '表卡编号',
					prop: 'archivesIdentity',
					attrs: {
						clearable: true,
						placeholder: '请输入表卡编号',
					},
				},
				{
					type: 'el-input',
					label: '企业编号',
					prop: 'enterpriseNumber',
					attrs: {
						clearable: true,
						placeholder: '请输入企业编号',
					},
				},
				{
					type: 'el-input',
					label: '托收协议号',
					prop: 'collectionAgreementNumber',
					attrs: {
						clearable: true,
						placeholder: '请输入托收协议号',
					},
				},
				{
					type: 'el-select',
					label: '业务来源',
					prop: 'businessType',
					options: [
						{
							label: '常规账单',
							value: 1,
						},
						{
							label: '罚没款账单',
							value: 2,
						},
					],
					attrs: {
						clearable: true,
						placeholder: '请选择业务来源',
					},
				},
				{
					type: 'el-input',
					label: '发票号码',
					prop: 'invoiceNo',
					attrs: {
						clearable: true,
						placeholder: '请输入发票号码',
					},
				},
				{
					type: 'el-select',
					label: '开票状态',
					prop: 'invoiceState',
					options:
						this.$store.getters?.dataList?.invoiceState?.map(item => {
							return {
								label: item.sortName,
								value: item.sortValue,
							}
						}) || [],
					attrs: {
						clearable: true,
						placeholder: '请选择开票状态',
					},
				},
			],
			formAttrs: { inline: true, labelWidth: '90px' },
			loading: false,
			tableData: [],
			columns: [
				{
					key: 'invoiceOpenTime',
					name: '开票日期',
					tooltip: true,
				},
				{
					key: 'invoiceNo',
					name: '发票号码',
					tooltip: true,
				},
				{
					key: 'invoiceState',
					name: '开票状态',
					tooltip: true,
					render: function (h, row) {
						const invoiceState = row.invoiceState
						const item = this.$store.getters?.dataList?.invoiceState?.find(
							item => item.sortValue == invoiceState,
						)
						return h('span', {}, item ? item.sortName : invoiceState)
					},
				},
				{
					key: 'archivesIdentitys',
					name: '档案编号',
					tooltip: true,
				},
				{
					key: 'enterpriseNumbers',
					name: '企业编号',
					tooltip: true,
				},
				{
					key: 'payerName',
					name: '发票抬头',
					tooltip: true,
				},
				{
					key: 'orderAmount',
					name: '开票金额(元)',
					tooltip: true,
					align: 'right',
					render: function (h, row) {
						const amount = row.orderAmount - 0
						return h('span', {}, amount.toFixed(2))
					},
				},
				{
					key: 'invoiceType',
					name: '开票类型',
					tooltip: true,
				},
				{
					key: 'businessType',
					name: '业务来源',
					tooltip: true,
					render: function (h, row) {
						const typeMap = {
							1: '常规账单',
							2: '罚没款账单',
						}
						const type = row.businessType
						return h('span', {}, typeMap[type] || '其他')
					},
				},
				{
					key: 'archivesCount',
					name: '表卡数量',
					align: 'right',
				},
				{
					key: 'billCount',
					name: '账单数量',
					align: 'right',
				},
			],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
		}
	},

	created() {
		this.getList()
	},
	methods: {
		getfilterNameFn(options = [], val, key = 'sortValue', label = 'sortName') {
			return getfilterName(options, val, key, label)
		},

		handlePageChange({ page, size }) {
			this.pageData.size = size
			this.getList(page)
		},
		handleReset() {
			this.$refs.formRef.resetForm()
			this.formData.dateRange = [this.dayStart, this.dayEnd]
			this.getList(1)
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.getList(1)
			}
		},
		async getList(curPage) {
			this.loading = true
			if (curPage) {
				this.pageData.current = curPage
			}
			try {
				const { current, size } = this.pageData
				const { dateRange = [], ...rest } = this.formData
				const { total = 0, records = [] } = await getInvoiceRecordList({
					size,
					current,
					...rest,
					invoiceOpenTimeStart: dateRange?.length > 0 ? dateRange[0] : '',
					invoiceOpenTimeEnd: dateRange?.length > 0 ? dateRange[1] : '',
				})
				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				console.error(error)
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		// 查看开票
		async handleCheckInvoicePdf(row) {
			const res = await getInvoicePdf({
				invoiceNo: row.invoiceNo,
			})
			if (res) {
				window.open(res, '_blank')
			}
		},
		// 查看缴费记录、关联账单记录
		handleRecord(row, tabName) {
			const path = row.userType == 3 ? '/meterManage/residentMeterView' : '/meterManage/companyMeterView'
			this.$router.push({
				path,
				query: {
					archivesId: row.archivesId,
					tabName,
				},
			})
		},
		handleDbClick(obj) {
			const invoiceRecordId = obj.row?.invoiceRecordId
			this.$router.push({
				name: 'invoiceRecordDetail',
				query: {
					invoiceRecordId,
				},
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
	padding: 20px;
}
::v-deep {
	.el-form .el-date-editor {
		width: 330px;
	}
}
</style>
