<template>
	<div class="wrapper" v-loading.fullscreen.lock="loading">
		<div class="left-wrapper">
			<div class="left-title">档案简目录</div>
			<GcTab :tabList="tabList" @changeTab="v => (activeTab = v)" :defaultTab="activeTab" />
		</div>
		<div class="right-wrapper">
			<RecordChoose
				ref="recordChooseRef"
				v-show="activeTab == 'recordChoose'"
				@changeTab="v => (activeTab = v)"
				:detailData="detailData"
				@getValid="getValid"
			/>
			<AddressChoose
				ref="addressChooseRef"
				v-show="activeTab == 'addressChoose'"
				:detailData="detailData"
				@changeTab="v => (activeTab = v)"
				@getValid="getValid"
			/>
			<UserInfo
				ref="userInfoRef"
				v-show="activeTab == 'userInfo'"
				:detailData="detailData"
				@changeTab="v => (activeTab = v)"
				@getValid="getValid"
			/>
			<MeterInfo
				ref="meterInfoRef"
				v-show="activeTab == 'meterInfo'"
				:detailData="detailData"
				@changeTab="v => (activeTab = v)"
				@getValid="getValid"
			/>
			<RecordsInfo
				ref="recordsInfoRef"
				v-show="activeTab == 'recordsInfo'"
				:detailData="detailData"
				@changeTab="v => (activeTab = v)"
				@getValid="getValid"
			/>
		</div>
		<div class="button-group">
			<el-button class="btn-preview" @click="handleCancel">取消</el-button>
			<el-button
				v-has="['cpm_archives_add2', 'cpm_archives_update']"
				class="btn-create"
				type="primary"
				@click="handleSubmit"
			>
				{{ isModify ? '保存修改' : '确定建档' }}
			</el-button>
		</div>
	</div>
</template>

<script>
import { removeNullParams, trimParams } from '@/utils/index.js'
import { apiArchivesAdd2, apiUpdateArchives, apiGetArchivesDetail } from '@/api/meterManage.api'
import RecordChoose from './record-choose'
import AddressChoose from './address-choose'
import MeterInfo from './meter-info'
import UserInfo from './user-info'
import RecordsInfo from './records-info'
export default {
	name: '',
	components: {
		RecordChoose,
		AddressChoose,
		MeterInfo,
		UserInfo,
		RecordsInfo,
	},
	data() {
		return {
			tabList: [
				{
					label: '册本选择',
					value: 'recordChoose',
					status: 1,
					disabled: false,
					tip: '待选择',
				},
				{
					label: '地址选择',
					value: 'addressChoose',
					status: 1,
					disabled: true,
					tip: '待完善',
				},
				{
					label: '用户信息',
					value: 'userInfo',
					status: 1,
					disabled: true,
					tip: '待完善',
				},
				{
					label: '表具信息',
					value: 'meterInfo',
					status: 1,
					disabled: true,
					tip: '待完善',
				},
				{
					label: '档案信息',
					value: 'recordsInfo',
					status: 1,
					disabled: true,
					tip: '待完善',
				},
			],
			activeTab: '',
			detailData: {}, // 详情回显的数据
			loading: false,
		}
	},
	computed: {
		isModify() {
			return this.$route.query.archivesId
		},
	},
	async activated() {
		const { archivesId } = this.$route.query
		if (archivesId) {
			await this._apiGetArchivesDetail({ archivesId })
		}
		this.$refs.recordChooseRef._getAlleyMap()
		this.$refs.addressChooseRef.updateAddress()
		this.$refs.meterInfoRef._apiGetMeterType()
		this.$refs.recordsInfoRef.updatePrice()
	},
	methods: {
		getValid(key, flag, errorMsg) {
			const obj = this.tabList.find(item => item.value === key)
			if (obj) {
				obj.status = flag ? 2 : 1
			}
			this.updateTabListStatus()
			if (key === 'recordChoose') {
				this.handleRecordChoose(flag, errorMsg)
			} else if (key === 'userInfo') {
				this.setArchivesIdentityRule() // 档案信息表卡编号校验规则重设
			}
		},
		updateTabListStatus() {
			// 册本未选择 禁用后续所有菜单，档案信息还根据用户信息是否完善来禁用
			const firstStatus = this.tabList[0]?.status
			const thirdStatus = this.tabList[2]?.status

			for (let i = 1; i < 4; i++) {
				this.tabList[i].disabled = firstStatus === 1
			}

			if (firstStatus === 2) {
				this.tabList[4].disabled = thirdStatus !== 2
			} else {
				this.tabList[4].disabled = true
			}
		},
		handleRecordChoose(flag, errorMsg) {
			if (flag) {
				const { bookNo, bookId, orgCode } = this.$refs.recordChooseRef.currentBookInfo || {}
				// 表具信息: 回显表册编号
				this.$refs.meterInfoRef.formData.bookNo = bookNo
				// 档案信息：设置查询表卡编号参数bookId,orgCode
				this.$refs.recordsInfoRef.archivesIdentityParams.bookId = bookId
				this.$refs.recordsInfoRef.archivesIdentityParams.orgCode = orgCode

				// 档案信息表卡编号校验规则重设
				this.setArchivesIdentityRule()
			} else {
				this.tabList[0].tip = errorMsg ? errorMsg : '待选择'
			}
		},
		setArchivesIdentityRule() {
			const { enterpriseNumber } = this.$refs.userInfoRef.formData
			// 档案信息：设置查询表卡编号参数enterpriseNumber
			const newEnterpriseNumber = enterpriseNumber.length !== 7 ? '' : enterpriseNumber
			this.$refs.recordsInfoRef.archivesIdentityParams.enterpriseNumber = newEnterpriseNumber
			// 档案信息：表卡编号校验规则
			this.$refs.recordsInfoRef.setArchivesIdentityRule({
				enterpriseNumber: newEnterpriseNumber,
			})
		},
		async _apiGetArchivesDetail(params) {
			const res = await apiGetArchivesDetail(params)
			this.detailData = Object.assign({}, ...Object.values(res))
		},
		handleCancel() {
			this.$store.dispatch('tagsView/delView', this.$route).then(tags => {
				const { fullPath } = tags.slice(-1)[0]
				this.$router.push(fullPath || '/')
			})
		},
		handleSubmit() {
			const obj = this.tabList.find(item => item.status == 1)
			if (obj) {
				this.$message.error(`${obj.value == 'recordChoose' ? '未选择册本' : obj.label + obj.tip}`)
				this.activeTab = obj.value
			} else {
				// 册本选择
				const bookObj = this.$refs.recordChooseRef.currentBookInfo
				console.log('册本信息', bookObj)
				// 地址选择
				const { addressAreaCode, addressFullName, formData } = this.$refs.addressChooseRef
				const addressParams = trimParams(removeNullParams(formData))
				addressParams['addressAreaCode'] = addressAreaCode
				addressParams['addressFullName'] = addressFullName
				console.log('地址选择', addressParams)

				// 用户信息
				const userInfoParams = trimParams(removeNullParams(this.$refs.userInfoRef.formData))
				const otherContactPhone = userInfoParams.mobileList ? userInfoParams.mobileList.join(',') : ''
				userInfoParams['userType'] = 4 // 4企业
				const businessLicenseUrl =
					userInfoParams.businessLicenseUrl && userInfoParams.businessLicenseUrl.length
						? JSON.stringify(
								userInfoParams.businessLicenseUrl.map(item => {
									return {
										name: item.name,
										url: item.url,
									}
								}),
						  )
						: ''
				const purchaseContractUrl =
					userInfoParams.purchaseContractUrl && userInfoParams.purchaseContractUrl.length
						? JSON.stringify(
								userInfoParams.purchaseContractUrl.map(item => {
									return {
										name: item.name,
										url: item.url,
									}
								}),
						  )
						: ''
				console.log('用户信息', userInfoParams)

				// 表具信息
				const meterInfoParams = trimParams(removeNullParams(this.$refs.meterInfoRef.formData))
				console.log('表具信息', meterInfoParams)

				// 档案信息
				const recordsInfoParams = trimParams(removeNullParams(this.$refs.recordsInfoRef.formData))
				console.log('档案信息', recordsInfoParams)

				const params = {
					orgCode: bookObj.orgCode,
					archives: {
						bookId: bookObj.bookId,
						businessLicenseUrl,
						purchaseContractUrl,
						archivesIdentity: recordsInfoParams.archivesIdentity,
						oldArchivesIdentity: recordsInfoParams.oldArchivesIdentity,
						summaryArchives: recordsInfoParams.summaryArchives,
						archivesMeterType: recordsInfoParams.archivesMeterType,
						accountNumber: recordsInfoParams.accountNumber,
						recordSeq: meterInfoParams.recordSeq,
						remark: meterInfoParams.remark,
						contractNum: userInfoParams.contractNum,
					},
					user: {
						userType: 4,
						userName: userInfoParams.userName,
						enterpriseNumber: userInfoParams.enterpriseNumber,
						contactPeople: userInfoParams.contactPeople,
						userMobile: userInfoParams.userMobile,
						contactPhone: userInfoParams.contactPhone,
						userSubType: userInfoParams.userSubType,
						chargingMethod: userInfoParams.chargingMethod,
						collectionAccountId: userInfoParams.collectionAccountId,
						otherContactPhone,
						invoiceType: userInfoParams.invoiceType,
						taxpayerIdentity: userInfoParams.taxpayerIdentity,
						openBank: userInfoParams.openBank,
						bankAccount: userInfoParams.bankAccount,
						zipCode: userInfoParams.zipCode,
						email: userInfoParams.email,
						mailingAddress: userInfoParams.mailingAddress,
						buyerName: userInfoParams.buyerName,
					},
					address: {
						regionCode: addressParams.regionCode,
						addressAreaCode: addressParams.addressAreaCode,
						addressFullName: addressParams.addressFullName + addressParams.addressName,
						addressName: addressParams.addressName,
						pressureZone: addressParams.pressureZone,
						tapWaterNo: addressParams.tapWaterNo,
						gisCode: addressParams.gisCode,
						houseYear: addressParams.houseYear,
						floorNum: addressParams.floorNum,
						pipeNetworkCode: addressParams.pipeNetworkCode,
					},
					meter: meterInfoParams,
					price: {
						priceId: recordsInfoParams.priceId,
					},
				}
				// 更新
				if (this.isModify) {
					const { archivesId, addressId, meterId, userId } = this.detailData
					params.archives['archivesId'] = archivesId
					params.address['addressId'] = addressId
					params.meter['meterId'] = meterId
					params.user['userId'] = userId
					this._apiUpdateArchives(params)
				} else {
					this._apiArchives(params)
				}
			}
		},
		async _apiArchives(params) {
			try {
				this.loading = true
				const { archivesId } = await apiArchivesAdd2(params)
				this.$store.dispatch('tagsView/delView', this.$route).then(() => {
					this.$router.push({
						path: '/meterManage/companyMeterView',
						query: {
							archivesId,
						},
					})
				})
			} catch (error) {
				console.error(error)
			} finally {
				this.loading = false
			}
		},
		async _apiUpdateArchives(params) {
			try {
				this.loading = true
				await apiUpdateArchives(params)
				this.$store.dispatch('tagsView/delView', this.$route).then(() => {
					this.$router.push({
						path: '/meterManage/companyMeterView',
						query: {
							archivesId: this.$route.query.archivesId,
						},
					})
				})
			} catch (error) {
				console.error(error)
			} finally {
				this.loading = false
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	display: flex;
	flex-wrap: wrap;
	height: 100%;
}
.left-wrapper {
	position: relative;
	padding: 20px;
	flex-grow: 0;
	flex-shrink: 0;
	width: 240px;
	height: calc(100% - 40px);
	background-color: #fff;
	.left-title {
		height: 48px;
		line-height: 48px;
		color: #000000;
		font-family: Source Han Sans CN;
		font-size: 16px;
		font-weight: 500;
	}
}
.left-wrapper:after {
	position: absolute;
	top: 20px;
	bottom: 20px;
	right: 0;
	content: '';
	display: block;
	clear: both;
	width: 1px;
	border-right: 1px dashed #eef0f3;
}
.right-wrapper {
	width: 0;
	flex: 1;
	padding: 20px;
	height: calc(100% - 40px);

	background-color: #fff;
}
.button-group {
	width: 100%;
	height: 40px;
	.btn-create,
	.btn-preview {
		margin-top: 20px;
		border-radius: 4px;
		height: 32px;
	}
	.btn-create {
		width: 216px;
	}
	.btn-preview {
		width: 110px;
		border: 1px solid #2f87fe;
		color: #2f87fe;
	}
}
</style>
