<template>
	<div class="invoice-info-select">
		<div class="tip-text">请选择开票信息</div>
		<div class="container">
			<div
				class="content-item"
				:class="{ active: radioValue === item.value }"
				@click="radioValue = item.value"
				v-for="(item, index) in radioList"
				:key="index"
			>
				<el-radio v-model="radioValue" :label="item.value"></el-radio>
				<div class="form">
					<el-row :gutter="40">
						<el-col v-for="(subItem, subIndex) in item.list" :key="subIndex" :span="12" class="kv-cell">
							<div class="label">{{ subItem.label }}</div>
							<div class="value">{{ subItem.value }}</div>
						</el-col>
					</el-row>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { queryInvoiceInfoList } from '@/api/print.api'
export default {
	name: 'invoice-info-select',
	props: {
		archivesIds: {
			type: [String, Number, Array],
			default: '',
		},
		isShow: {
			type: Boolean,
			default: false,
		},
		queryExtraParams: {
			type: Object,
			default: () => {
				return {}
			},
		},
	},
	data() {
		return {
			radioValue: null,
			radioList: [],
			loading: null,
			querying: false,
		}
	},
	computed: {
		selectedInvoiceInfo() {
			if (!this.radioValue) return null
			const selected = this.radioList.find(item => item.value === this.radioValue)
			return selected ? selected.original : null
		},
	},
	methods: {
		queryInvoiceInfoList() {
			if (!this.archivesIds || !this.isShow) {
				return
			}
			if (this.querying) return
			this.querying = true
			this.radioValue = null
			if (this.loading) {
				this.loading.close()
				this.querying = false
			}
			this.loading = this.$loading({
				target: this.$el,
				lock: true,
				text: '开票信息查询中...',
				spinner: 'el-icon-loading',
				background: 'rgba(255, 255, 255, 0.5)',
			})
			const archivesIds = Array.isArray(this.archivesIds) ? this.archivesIds : [this.archivesIds]
			queryInvoiceInfoList({ archivesIds, ...this.queryExtraParams })
				.then(res => {
					this.radioList = res.map(item => {
						return {
							value: item.invoiceBuyerId,
							original: item,
							list: [
								{
									label: '用户名称',
									value: item.userName,
								},
								{
									label: '纳税人识别号',
									value: item.taxpayerIdentity,
								},
								{
									label: '开户银行',
									value: item.openBank,
								},
								{
									label: '银行账号',
									value: item.bankAccount,
								},
							],
						}
					})
					const defaultInfo = res.find(item => item.defualt)
					if (defaultInfo) {
						this.radioValue = defaultInfo.invoiceBuyerId
						return
					}
					if (!this.radioList.length) {
						this.radioValue = null
						this.$emit('error', '无开票信息')
					}
				})
				.catch(err => {
					this.radioList = []
					this.$emit('error', '开票信息列表查询失败')
					console.error(err)
				})
				.finally(() => {
					if (this.loading) {
						this.loading.close()
						this.loading = null
					}
					this.querying = false
				})
		},
	},
	watch: {
		archivesIds(val) {
			if (val) {
				this.queryInvoiceInfoList()
			}
		},
		selectedInvoiceInfo: {
			immediate: true,
			handler(val) {
				this.$emit('select', val)
			},
		},
		isShow(val) {
			if (val) {
				this.queryInvoiceInfoList()
			}
		},
	},
	mounted() {
		this.queryInvoiceInfoList()
	},
}
</script>

<style lang="scss" scoped>
.invoice-info-select {
	min-height: 240px;
}
.tip-text {
	height: 3rem;
	line-height: 3rem;
	font-size: 14px;
	position: sticky;
	top: 0;
	background: #fff;
	z-index: 10;
}
.container {
	margin: 1em 0;
	min-height: 150px;
	padding: 0 !important;
	.active {
		border-color: #2f87fe !important;
	}
	.content-item {
		padding: 16px;
		cursor: pointer;
		margin-bottom: 16px;
		border: 1px solid #eef0f3;
		display: flex;
		align-items: flex-start;
		&:hover {
			border-color: #abd0ff !important;
		}
		& > .form {
			width: calc(100% - 44px);
		}
		.kv-cell {
			display: flex;
			align-items: flex-end;
			gap: 15px;
			padding: 5px 0;
		}
		.label {
			font-family: Source Han Sans CN;
			font-size: 12px;
			font-weight: 400;
			line-height: 1.1;
			color: #999999;
			width: 6em;
			flex-grow: 0;
			flex-shrink: 0;
			white-space: nowrap;
			text-align: right;
		}
		.value {
			font-family: Source Han Sans CN;
			font-size: 14px;
			font-weight: 400;
			line-height: 1;
			color: #4e4e4e;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			vertical-align: bottom;
		}
	}

	::v-deep {
		.el-radio {
			margin-top: 2px;
		}
		.el-radio__label {
			display: none;
		}
		.el-col {
			margin-bottom: 2px;
		}
	}
}
</style>
