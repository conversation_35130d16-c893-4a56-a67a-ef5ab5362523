// 0-100 整数
const regex1Integer = /^(100|[1-9]?\d?)$/
// 0-999999999 整数
const regex2Integer = /^(999999999|[1-9]\d{0,8}|0)$/

import { getfilterName } from '@/utils'
import { subDistributionModeOptions } from '@/consts/optionList.js'
export function getColumn(_this) {
	const renderNumber = (h, row, total, scope, editKey, regex) => {
		const key = scope.column.property
		const index = scope.$index
		const newRow = _this.tableData[index]
		const btnOne = [
			h(
				'span',
				{
					style: {
						color: '#ec6b60',
					},
				},
				'*',
			),
			h(
				'span',
				{
					style: {
						marginRight: '10px',
						lineHeight: '28px',
					},
				},
				newRow[key],
			),
			h('i', {
				class: 'el-icon-edit',
				style: {
					cursor: 'pointer',
				},
				on: {
					click: () => {
						newRow[editKey] = true
						_this.tableKey += 1
						_this.validateTable()
					},
				},
			}),
		]
		const btnTwo = [
			h(
				'span',
				{
					style: {
						color: '#ec6b60',
						marginRight: '4px',
					},
				},
				'*',
			),
			h('el-input', {
				style: {
					marginRight: '10px',
				},
				props: {
					value: row[key],
					size: 'mini',
				},
				on: {
					input: value => {
						if (regex.test(value) || value === '') {
							row[key] = value
						}
					},
					blur: event => {
						newRow[editKey] = false
						newRow[key] = event.target.value
						_this.tableKey += 1
						_this.validateTable()
					},
				},
			}),
			h('i', {
				class: 'el-icon-check',
				style: {
					cursor: 'pointer',
				},
				on: {
					click: () => {
						newRow[editKey] = false
						_this.tableKey += 1
						_this.validateTable()
					},
				},
			}),
		]
		const btnArr = newRow[editKey] ? btnTwo : btnOne
		return h(
			'div',
			{
				style: {
					display: 'flex',
					justifyContent: 'space-between',
					alignItems: 'center',
				},
			},
			btnArr,
		)
	}
	// 下拉
	const renderSelect = (h, row, total, scope, editKey, options) => {
		const key = scope.column.property
		const index = scope.$index
		const newRow = _this.tableData[index]
		const optionElements = options.map(option =>
			h('el-option', {
				attrs: {
					value: option.value,
					label: option.label,
					disabled: option.disabled,
				},
			}),
		)
		const btnOneValue = options.find(option => option.value === newRow[key])
		const btnOne = [
			h(
				'span',
				{
					style: {
						color: '#ec6b60',
					},
				},
				'*',
			),
			h(
				'span',
				{
					style: {
						marginRight: '10px',
					},
				},
				btnOneValue && btnOneValue.label,
			),
			h('i', {
				class: 'el-icon-edit',
				style: {
					cursor: 'pointer',
				},
				on: {
					click: () => {
						newRow[editKey] = true
						_this.tableKey += 1
						_this.validateTable()
					},
				},
			}),
		]
		const btnTwo = [
			h(
				'span',
				{
					style: {
						color: '#ec6b60',
						marginRight: '4px',
					},
				},
				'*',
			),
			h(
				'el-select',
				{
					attrs: {
						value: newRow[key],
						clearable: true,
						filterable: true,
						placeholder: '请选择',
						size: 'mini',
					},
					on: {
						change: value => {
							newRow[key] = value
							newRow[editKey] = false
							_this.tableKey += 1
							_this.validateTable()
							if (key == 'subDistributionMode') {
								// 总分配方式为2且子分配方式为剩余量占比时，分配额默认为100
								if (_this.radioValue === 2 && value === 2) {
									newRow.usageMeasure = 100
								} else {
									newRow.usageMeasure = ''
								}
							}

							if (key === 'priceCode') {
								_this.assignPriceInfo(index) // 回显价格信息
							}
						},
					},
				},
				optionElements,
			),
			h('i', {
				class: 'el-icon-check',
				style: {
					cursor: 'pointer',
				},
				on: {
					click: () => {
						newRow[editKey] = false
						_this.tableKey += 1
						_this.validateTable()
					},
				},
			}),
		]
		const btnArr = newRow[editKey] ? btnTwo : btnOne
		return h(
			'div',
			{
				style: {
					display: 'flex',
					justifyContent: 'space-between',
					alignItems: 'center',
				},
			},
			btnArr,
		)
	}

	const actionColumn = {
		key: 'actionSlot',
		name: '操作',
		fixed: 'right',
		hide: !Boolean(_this.$route.query.archivesId),
	}

	const baseColumn = [
		{
			key: 'index',
			name: '序号',
			width: 60,
		},
		{
			key: 'archivesIdentity',
			name: '表卡编号',
			tooltip: true,
			render: (h, row, total, scope) => {
				// 企业表卡11位  居民表卡9位
				const regx = _this.realMeterData?.userType == 3 ? /^\d{1,9}$/ : /^\d{1,11}$/
				return !row.virtualMeterType || row.archivesStatus == 3 || row.archivesId
					? h('span', {}, row[scope.column.property])
					: renderNumber(h, row, total, scope, 'isArchivesIdentityEdit', regx)
			},
		},
		{
			key: 'virtualMeterType',
			name: '表卡类型',
			tooltip: true,
			width: '80',
			render: (h, row, total, scope) => {
				const key = scope.column.property
				return h(
					'span',
					{},
					getfilterName(_this.$store.getters.dataList.virtualMeterType, row[key], 'sortValue', 'sortName'),
				)
			},
		},
		{
			key: 'priceCode',
			name: '价格编号',
			render: (h, row, total, scope) => {
				// 有档案id  禁止编辑价格编号
				return row.archivesId
					? h('span', {}, row[scope.column.property])
					: renderSelect(h, row, total, scope, 'isPriceCodeEdit', _this.priceOptions)
			},
		},
		{
			key: 'priceName',
			name: '价格名称',
			tooltip: true,
		},
		{
			key: 'natureName',
			name: '用水性质',
			tooltip: true,
		},
		{
			key: 'archivesStatus',
			name: '表卡状态',
			tooltip: true,
			render: (h, row, total, scope) => {
				const key = scope.column.property
				return h(
					'span',
					{},
					getfilterName(_this.$store.getters.dataList.archiveState, row[key], 'sortValue', 'sortName'),
				)
			},
		},
	]

	if (_this.radioValue === 0) {
		const newBaseColumn = baseColumn.slice(0)
		newBaseColumn.splice(3, 0, {
			key: 'usageMeasure',
			name: '用水比例（%）',
			render: (h, row, total, scope) => {
				return row.archivesStatus == 3
					? h('span', {}, row[scope.column.property])
					: renderNumber(h, row, total, scope, 'isWaterUseRatioEdit', regex1Integer)
			},
		})
		newBaseColumn.push(
			{
				key: 'operateSort',
				name: '排序',
				width: 90,
				fixed: 'right',
			},
			actionColumn,
		)
		return newBaseColumn
	} else if (_this.radioValue === 1) {
		const newBaseColumn = baseColumn.slice(0)
		newBaseColumn.splice(3, 0, {
			key: 'usageMeasure',
			name: '固定水量（吨）',
			render: (h, row, total, scope) => {
				return row.archivesStatus == 3
					? h('span', {}, row[scope.column.property])
					: renderNumber(h, row, total, scope, 'isGdWaterEdit', regex2Integer)
			},
		})
		newBaseColumn.push(
			{
				key: 'operateSort',
				name: '排序',
				width: 100,
				fixed: 'right',
			},
			actionColumn,
		)
		return newBaseColumn
	} else if (_this.radioValue === 2) {
		const newBaseColumn = baseColumn.slice(0)
		newBaseColumn.splice(
			3,
			0,
			{
				key: 'subDistributionMode',
				name: '分配方式',
				minWidth: 180,
				render: (h, row, total, scope) => {
					return row.archivesStatus == 3
						? h('span', {}, getfilterName(subDistributionModeOptions, row.subDistributionMode))
						: renderSelect(h, row, total, scope, 'isSubDistributionModeEdit', subDistributionModeOptions)
				},
			},
			{
				key: 'usageMeasure',
				name: '分配额',
				render: (h, row, total, scope) => {
					const regexValue = row.subDistributionMode == 1 ? regex2Integer : regex1Integer

					return row.archivesStatus == 3
						? h('span', {}, row[scope.column.property])
						: row.subDistributionMode == 2
						? h('span', {}, 100)
						: renderNumber(h, row, total, scope, 'isAllocationQuotaEdit', regexValue)
				},
			},
		)
		newBaseColumn.push(
			{
				key: 'operateSort',
				name: '排序',
				width: 100,
				fixed: 'right',
			},
			actionColumn,
		)
		return newBaseColumn
	} else if (_this.radioValue === 4) {
		const allocationMethodOptions = subDistributionModeOptions.filter(item => item.value !== 0)
		const newBaseColumn = baseColumn.slice(0)
		newBaseColumn.splice(
			3,
			0,
			{
				key: 'subDistributionMode',
				name: '分配方式',
				minWidth: 180,
				render: (h, row, total, scope) => {
					return row.archivesStatus == 3
						? h('span', {}, getfilterName(allocationMethodOptions, row.subDistributionMode))
						: renderSelect(h, row, total, scope, 'isSubDistributionModeEdit', allocationMethodOptions)
				},
			},
			{
				key: 'usageMeasure',
				name: '分配额',
				render: (h, row, total, scope) => {
					const regexValue = row.subDistributionMode == 1 ? regex2Integer : regex1Integer

					return row.archivesStatus == 3
						? h('span', {}, row[scope.column.property])
						: renderNumber(h, row, total, scope, 'isAllocationQuotaEdit', regexValue)
				},
			},
		)
		newBaseColumn.push(
			{
				key: 'operateSort',
				name: '排序',
				width: 100,
				fixed: 'right',
			},
			actionColumn,
		)
		return newBaseColumn
	} else {
		const newBaseColumn = baseColumn.slice(0)
		newBaseColumn.push(actionColumn)
		return newBaseColumn
	}
}
