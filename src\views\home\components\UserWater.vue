<template>
	<CardContainer :cardDetail="cardDetail">
		<div class="content-box">
			<div class="top">
				<el-select v-model="selValue" placeholder="请选择" @change="getList">
					<el-option
						v-for="item in natureOptions"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					></el-option>
				</el-select>
				<el-radio-group v-model="tabValue" @input="getList">
					<el-radio-button :label="0">月</el-radio-button>
					<el-radio-button :label="1">年</el-radio-button>
				</el-radio-group>
			</div>
			<div class="bottom">
				<GcBar1
					:unit="chartOptions.unit"
					:xData="chartOptions.xData"
					:seriesData="chartOptions.seriesData"
					:seriesName="chartOptions.seriesName"
					:startDataZoom="50"
					:endDataZoom="60"
				/>
			</div>
		</div>
	</CardContainer>
</template>

<script>
import CardContainer from './CardContainer.vue'
import { apiGetWaterVolumeChart } from '@/api/home.api'
export default {
	name: 'UserWater',
	components: { CardContainer },
	props: {
		natureOptions: {
			type: Array,
			default: () => [],
		},
		orgCode: {
			type: String,
			default: '',
		},
	},
	watch: {
		natureOptions: {
			handler: function (arr) {
				if (arr && arr.length) {
					this.selValue = arr[0].value
				}
			},
			deep: true,
		},
		orgCode() {
			this.getList()
		},
		'cardDetail.activeTab'(v) {
			const typeMap = this.cardDetail.tabList.reduce((map, item) => {
				map[item.value] = item.name.replace('表卡', '')
				return map
			}, {})
			this.chartOptions.seriesName = typeMap[v] || ''
			this.getList()
		},
	},
	data() {
		return {
			cardDetail: {
				titleList: [
					{
						label: '当月水量',
						value: '--',
						unit: 'm³',
					},
					{
						label: '同期比',
						value: '--',
						unit: '%',
					},
					{
						label: '全年累计',
						value: '--',
						unit: 'm³',
					},
					{
						label: '同期比',
						value: '--',
						unit: '%',
					},
				],
				activeTab: '3',
				tabList: [
					{
						name: '居民表卡',
						value: '3',
					},
					{
						name: '企业表卡',
						value: '4',
					},
				],
				bg: require('@/assets/images/bg/home-bg1.png'),
				name: '用户水量',
			},
			tabValue: 0,
			selValue: '',
			chartOptions: {
				unit: '单位: m³',
				xData: [],
				seriesData: [],
				seriesName: '居民',
			},
		}
	},
	methods: {
		async getList() {
			try {
				const {
					annualTotal,
					currentMonthAmount,
					monthSameRatio,
					yearSameRatio,
					natureAmountList = [],
				} = await apiGetWaterVolumeChart({
					dimension: this.tabValue,
					orgCode: this.orgCode,
					userType: this.cardDetail.activeTab,
				})
				this.cardDetail.titleList[0].value = currentMonthAmount
				this.cardDetail.titleList[1].value = monthSameRatio
				this.cardDetail.titleList[2].value = annualTotal
				this.cardDetail.titleList[3].value = yearSameRatio
				if (Array.isArray(natureAmountList) && natureAmountList.length) {
					const result = natureAmountList.find(item => item.natureNo === this.selValue)
					if (result && Array.isArray(result.children) && result.children.length) {
						this.chartOptions.xData = result.children.map(item => item.natureName)
						this.chartOptions.seriesData = result.children.map(item => item.useAmount)
						this.chartOptions.xData.unshift(result.natureName)
						this.chartOptions.seriesData.unshift(result.useAmount)
					}
				}
			} catch (error) {
				console.error(error)
			}
		},
	},
}
</script>
