import Layout from '@/layout'

export default [
	{
		path: '/arrearageManage',
		name: 'ArrearageManage',
		component: Layout,
		redirect: '/arrearageManage/outstandingBills',
		meta: {
			title: '欠费',
			icon: 'icon-cis_yj_qianfei',
			permissions: [
				'cpm_urge_payment_queryBillPage',
				'cpm_urge_payment_printNotice',
				'cpm_urge_payment_urgeRegister',
				'cpm_urge_payment_queryUrgeRecordPage',
			],
		},
		children: [
			{
				path: 'outstandingBills',
				name: 'OutstandingBills',
				component: () => import('@/views/arrearage-manage/outstanding-bills/index.vue'),
				meta: {
					title: '待催缴账单',
					keepAlive: true,
					icon: 'icon-cis_ej_daicuijiao',
					permissions: [
						'cpm_urge_payment_queryBillPage',
						'cpm_urge_payment_printNotice',
						'cpm_urge_payment_urgeRegister',
					],
				},
			},
			{
				path: 'callRecord',
				name: '<PERSON><PERSON><PERSON><PERSON>',
				component: () => import('@/views/arrearage-manage/call-record/index.vue'),
				meta: {
					title: '催缴记录',
					keepAlive: true,
					icon: 'icon-cis_ej_cuijiaojilu',
					permissions: ['cpm_urge_payment_queryUrgeRecordPage'],
				},
			},
		],
	},
]
