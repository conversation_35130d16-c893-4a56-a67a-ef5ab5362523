<template>
	<CardContainer :cardDetail="cardDetail">
		<div class="content-box">
			<div class="bottom">
				<Gcline1
					:unit="chartOptions.unit"
					:xData="chartOptions.xData"
					:seriesData="chartOptions.seriesData"
					:seriesName="chartOptions.seriesName"
					:legend="chartOptions.legend"
					:showDataZoom="false"
					:axisLabelRotate="0"
				/>
			</div>
		</div>
	</CardContainer>
</template>

<script>
import CardContainer from './CardContainer.vue'
import { apiGetMeterReadingRateChart } from '@/api/home.api'
export default {
	name: 'ReadingRate',
	components: { CardContainer },
	props: {
		orgCode: {
			type: String,
			default: '',
		},
	},
	watch: {
		orgCode() {
			this.getList()
		},
	},
	data() {
		return {
			cardDetail: {
				name: '实际抄表率',
				bg: require('@/assets/images/bg/home-bg3.png'),
				titleList: [
					{
						label: '居民表卡实抄率',
						value: '--',
						unit: '%',
					},
					{
						label: '企业表卡实抄率',
						value: '--',
						unit: '%',
					},
				],
			},

			tabValue: 'month',
			selValue: '',
			chartOptions: {
				unit: '单位: %',
				xData: [],
				seriesData: [],
				seriesName: '企业',
				legend: {
					padding: [30, 0, 0, 0],
				},
			},
		}
	},

	methods: {
		async getList() {
			try {
				this.chartOptions.seriesData = []
				const { residentRatio, enterpriseRatio, enterpriseMeterReadingRateList, residentMeterReadingRateList } =
					await apiGetMeterReadingRateChart({
						orgCode: this.orgCode,
						userType: this.cardDetail.activeTab,
					})
				this.cardDetail.titleList[0].value = residentRatio
				this.cardDetail.titleList[1].value = enterpriseRatio
				if (Array.isArray(enterpriseMeterReadingRateList) && enterpriseMeterReadingRateList.length) {
					this.chartOptions.xData = enterpriseMeterReadingRateList.map(item => item.month)
					const data = enterpriseMeterReadingRateList.map(item => item.ratio)
					this.chartOptions.seriesData.push({
						name: '企业',
						data: data,
						type: 'line',
						smooth: true,
						color: '#FF928A',
						areaStyle: {
							color: new window.echarts.graphic.LinearGradient(0, 0, 0, 1, [
								{ offset: 0, color: 'rgba(255, 146, 138, 0.3)' },
								{ offset: 1, color: 'rgba(255, 146, 138, 0.05)' },
							]),
						},
					})
				}
				if (Array.isArray(residentMeterReadingRateList) && residentMeterReadingRateList.length) {
					this.chartOptions.xData = residentMeterReadingRateList.map(item => item.month)
					const data = residentMeterReadingRateList.map(item => item.ratio)
					this.chartOptions.seriesData.push({
						name: '居民',
						data: data,
						type: 'line',
						smooth: true,
						color: '#8979FF',
						areaStyle: {
							color: new window.echarts.graphic.LinearGradient(0, 0, 0, 1, [
								{ offset: 0, color: '#8979FF4D' },
								{ offset: 1, color: '#8979FF0D' },
							]),
						},
					})
				}
				console.log(222222222, this.chartOptions.seriesData)
			} catch (error) {
				console.error(error)
			}
		},
	},
}
</script>
