import { accSub } from '@/utils/calc.js'
export function getColumn(_this) {
	const baseColumn = [
		{
			key: 'meterBalanceAmount',
			name: '预存余额',
			tooltip: true,
		},
		{
			key: 'arrearsCount',
			name: '欠费笔数',
			tooltip: true,
		},
		{
			key: 'arrearsAmountSum',
			name: '欠费总额',
			tooltip: true,
		},
		{
			key: 'waterAmountSum',
			name: '总水费',
			tooltip: true,
		},
		{
			key: 'billItemAmountSum',
			name: '总污水费',
			tooltip: true,
		},

		{
			fixed: 'right',
			name: '操作',
			minWidth: 220,
			tooltip: true,
			render: (h, row) => {
				const value = accSub(row.meterBalanceAmount, row.arrearsAmountSum).toFixed(2)

				const btnOne = h(
					'el-button',
					{
						props: {
							type: 'text',
							size: 'medium',
						},
						on: {
							click: () => {
								const value = row.meterBalanceAmount
								let label = ''
								if (_this.activeTab === 'resident') {
									const archivesIdentity = row.archivesIdentity
									label = `当前表卡${archivesIdentity}余额有${value}元，确定继续余额销账`
								} else {
									const enterpriseNumber = row.enterpriseNumber
									label = `当前企业${enterpriseNumber}余额有${value}元，确定继续余额销账`
								}
								_this
									.$confirm(label, '提示', {
										confirmButtonText: '确定',
										cancelButtonText: '取消',
										type: 'warning',
										center: true,
									})
									.then(() => {
										const params = {
											billDate: row.billDate,
										}
										if (_this.activeTab === 'resident') {
											params.archivesIdList = [row.archivesId]
										} else {
											params.userIdList = [row.userId]
										}
										_this._apiClearAccount(params)
									})
									.catch(() => {})
							},
						},
					},
					'销账',
				)
				const btn = _this.$has('billing_bill_account-clear') && value >= 0 ? btnOne : '' // 必要条件 有权限 且 预存金额大于等于欠费总额 才显示销账按钮
				const query =
					_this.activeTab === 'resident'
						? {
								archivesIdentity: row.archivesIdentity,
						  }
						: {
								enterpriseNumber: row.enterpriseNumber,
						  }
				return h('div', {}, [
					btn,
					h(
						'el-button',
						{
							props: {
								type: 'text',
								size: 'medium',
							},
							on: {
								click: () => {
									_this.$router.push({
										path: '/costManage/billManage',
										query,
									})
								},
							},
						},
						'查看欠费明细',
					),
				])
			},
		},
	]
	const residentColumns = [
		{
			key: 'archivesIdentity',
			name: '表卡编号',
			tooltip: true,
		},
		{
			key: 'userName',
			name: '用户名',
			tooltip: true,
		},
		{
			key: 'addressFullName',
			name: '地址',
			tooltip: true,
		},
	]
	const companyColumns = [
		{
			key: 'enterpriseNumber',
			name: '企业编号',
			tooltip: true,
		},
		{
			key: 'userName',
			name: '企业名称',
			tooltip: true,
		},
		{
			key: 'companyAddress',
			name: '企业地址',
			tooltip: true,
		},
		{
			key: 'archivesCount',
			name: '实表表卡数量',
		},
	]
	let column = []
	if (_this.activeTab === 'resident') {
		column = residentColumns.concat(baseColumn)
	} else {
		column = companyColumns.concat(baseColumn)
	}
	return column
}
