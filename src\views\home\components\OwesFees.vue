<template>
	<CardContainer :cardDetail="cardDetail">
		<div class="owes-fees">
			<!-- <GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				showPage
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				@current-page-change="changePage"
			/> -->
			<GcTable :loading="loading" :columns="columns" :table-data="tableData" />
		</div>
	</CardContainer>
</template>

<script>
import CardContainer from './CardContainer.vue'
import { apiGetWaterFeeArrearsChart } from '@/api/home.api'
export default {
	name: 'OwesFees',
	components: { CardContainer },
	props: {
		orgCode: {
			type: String,
			default: '',
		},
	},
	watch: {
		orgCode(v) {
			if (v) {
				if (!this.$has('cpm_home_charts_waterFeeArrearsChart')) {
					return
				}
				this.getList()
			}
		},
		'cardDetail.activeTab'() {
			this.getList()
		},
	},
	data() {
		return {
			cardDetail: {
				titleList: [
					{
						label: '当月欠费',
						value: '--',
						unit: '万元',
					},
					{
						label: '全年累计欠费',
						value: '--',
						unit: '万元',
					},
				],
				activeTab: '3',
				tabList: [
					{
						name: '居民表卡',
						value: '3',
					},
					{
						name: '企业表卡',
						value: '4',
					},
				],
				bg: require('@/assets/images/bg/home-bg2.png'),
				name: '用户欠费',
			},
			columns: [
				{
					key: 'staffName',
					name: '抄表员',
					tooltip: true,
				},
				{
					key: 'cbjs',
					name: '抄表件数(个)',
					tooltip: true,
					width: 120,
				},
				{
					key: 'feeAmt',
					name: '水费金额(万元)',
					tooltip: true,
					width: 120,
				},
				{
					key: 'waterFeeBackRate',
					name: '水费回收率(%)',
					width: 120,
					tooltip: true,
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			loading: false,
		}
	},

	methods: {
		async getList() {
			try {
				const { currentMonthArrearsAmt, annualTotal, amtBackList } = await apiGetWaterFeeArrearsChart({
					dimension: this.tabValue,
					orgCode: this.orgCode,
					userType: this.cardDetail.activeTab,
					// current: this.pageData.current,
					// size: this.pageData.size,
				})
				this.cardDetail.titleList[0].value = currentMonthArrearsAmt
				this.cardDetail.titleList[1].value = annualTotal

				if (Array.isArray(amtBackList) && amtBackList.length) {
					this.tableData = amtBackList
				}
			} catch (error) {
				console.error(error)
			}
		},
		changePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
	},
}
</script>

<style lang="scss" scoped>
::v-deep {
	.chart-box {
		display: flex;
		flex-direction: column;
	}
}
.owes-fees {
	flex: 1;
	height: 0;
}
</style>
