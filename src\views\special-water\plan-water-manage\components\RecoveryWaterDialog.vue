<template>
	<gc-el-dialog
		:show="isShow"
		title="追缴水量"
		custom-top="120px"
		width="960px"
		@open="handleOpen"
		@close="handleClose"
	>
		<div class="data-box">
			<div class="data-item">
				<div class="label">企业编号：</div>
				<div class="value">{{ formData.enterpriseNumber || '--' }}</div>
			</div>
			<div class="data-item">
				<div class="label">企业名称：</div>
				<div class="value">{{ formData.enterpriseName || '--' }}</div>
			</div>
			<div class="data-item">
				<div class="label">计划年：</div>
				<div class="value">{{ formData.planYear || '--' }}</div>
			</div>
			<div class="data-item">
				<div class="label">计划用水量：</div>
				<div class="value">{{ formData.planUsageAmt || '--' }}</div>
			</div>
			<div class="data-item">
				<div class="label">实际已用水量：</div>
				<div class="value">{{ formData.yearUsageAmt || '--' }}</div>
			</div>
		</div>
		<GcFormSimple
			ref="formRef"
			v-model="formData"
			:formItems="formItems"
			:formAttrs="formAttrs"
			style="padding-top: 12px"
		>
			<template #placeholder>
				<div style="width: 300px"></div>
			</template>
			<template #onlyRecoveryWaterFees>
				<el-form-item prop="onlyRecoveryWaterFees" label=" " label-width="0">
					<el-checkbox v-model="formData.onlyRecoveryWaterFees" :true-label="1" :false-label="0">
						仅追收水费
					</el-checkbox>
				</el-form-item>
			</template>
		</GcFormSimple>
		<GcTable ref="tableRef" :loading="loading" :columns="columns" :table-data="tableData">
			<!-- 水价 -->
			<template #usePrice="{ row }">
				<div v-show="row.usePrice" v-html="row.usePrice"></div>
				<span v-show="!row.usePrice">--</span>
			</template>
			<!-- 附加价格 -->
			<template #additionalPrice="{ row }">
				<div v-show="row.additionalPrice" v-html="row.additionalPrice" />
				<span v-show="!row.additionalPrice">--</span>
			</template>
			<!-- 附加费 -->
			<template #billItemAmt>
				<span>
					{{ formData.onlyRecoveryWaterFees === 0 ? currentBillItemAmt : '--' }}
				</span>
			</template>
		</GcTable>

		<template #footer>
			<gc-button btn-type="three" @click.native="handleClose">取消</gc-button>
			<gc-button :loading="saveLoading" @click.native="handleSave">保存</gc-button>
		</template>
	</gc-el-dialog>
</template>

<script>
import { accAdd } from '@/utils/calc'
import { apiEffectivePrice } from '@/api/meterManage.api.js'
import { queryArchives, queryRecoveryInfo, planUsageRecovery } from '@/api/specialWater.api'

export default {
	name: '',
	props: {
		show: {
			type: Boolean,
			default: false,
		},
	},
	computed: {
		// 组件内控制弹窗显示/隐藏
		isShow: {
			get: function () {
				return this.show
			},
			set: function (val) {
				this.$emit('update:show', val)
			},
		},

		// 当前选择的追收抄表表卡编号
		currentArchivesNoComp() {
			const target = this.formItems[0].options.find(item => item.value === this.formData.archivesId)
			return target ? target.label : '--'
		},
		// 当前选择的价格对象数据
		currentPriceDataComp() {
			const target = this.formItems[2].options.find(item => {
				return item.priceId === this.formData.priceId
			})
			return target ? target : {}
		},
		// 当前价格的附加费（各个附加费价格*水量的总和）
		currentBillItemAmt() {
			const data = this.currentPriceDataComp?.priceBillItemList || []
			const useAmount = this.formData.overPlanAmt
			const totalPrice = data.reduce((accumulator, currentValue) => {
				return accAdd(accumulator, currentValue.billItemPrice)
			}, 0)
			return data.length === 0 ? '--' : useAmount * totalPrice
		},
	},
	data() {
		return {
			formData: {
				enterpriseName: '',
				enterpriseNumber: '',
				planYear: '',
				planUsageAmt: '',
				yearUsageAmt: '',

				archivesId: '',
				priceId: '',
				onlyRecoveryWaterFees: 1,
			},
			formItems: [
				{
					type: 'el-select',
					label: '追收表卡：',
					prop: 'archivesId',
					options: [],
					attrs: {
						filterable: true,
						clearable: false,
						placeholder: '请选择追收表卡',
					},
					events: {
						change: () => {
							this.$set(this.tableData[0], 'archivesNo', this.currentArchivesNoComp)
						},
					},
				},
				{
					type: 'slot',
					slotName: 'placeholder',
				},
				{
					type: 'el-select',
					label: '价格：',
					prop: 'priceId',
					options: [],
					attrs: {
						clearable: false,
						placeholder: '请选择价格',
					},
					events: {
						change: () => {
							const data = this.currentPriceDataComp?.priceBillItemList || []
							const additionalPriceArr = data.map(item => {
								return `${item.itemName}: ${item.billItemPrice}元/吨`
							})
							this.$set(this.tableData[0], 'additionalPrice', additionalPriceArr.join('<br />'))

							// 获取水价、水费
							this.getRecoveryInfo()
						},
					},
				},
				{
					type: 'slot',
					slotName: 'onlyRecoveryWaterFees',
					prop: 'onlyRecoveryWaterFees',
				},
			],
			formAttrs: {
				inline: true,
				labelPosition: 'left',
				labelWidth: '110px',
				rules: {
					archivesId: [{ required: true, message: '请选择追收表卡', trigger: 'change' }],
					priceId: [{ required: true, message: '请选择价格', trigger: 'change' }],
				},
			},

			loading: false,
			tableData: [
				{
					archivesNo: '',
					useAmount: '',
					additionalPrice: '',
				},
			],
			columns: [
				{
					key: 'archivesNo',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'useAmount',
					name: '水量',
					tooltip: true,
				},
				{
					key: 'usePrice',
					name: '水价',
				},
				{
					key: 'usAmt',
					name: '水费',
					tooltip: true,
				},
				{
					key: 'additionalPrice',
					name: '附加价格',
				},
				{
					key: 'billItemAmt',
					name: '附加费',
					tooltip: true,
				},
			],

			saveLoading: false,
		}
	},
	created() {},
	methods: {
		// 获取追收表卡下拉数据
		async getArchives() {
			try {
				const res = await queryArchives({
					enterpriseNum: this.formData.enterpriseNumber,
				})
				const data = res || []
				this.formItems[0].options = data.map(item => {
					return {
						label: item.archivesNo,
						value: item.archivesId,
					}
				})
			} catch (error) {
				console.error(error)
				this.formItems[0].options = []
			}
		},
		// 获取价格下拉数据
		async getPriceList() {
			try {
				const res = await apiEffectivePrice()
				const data = res || []
				this.formItems[2].options = data.map(item => {
					return {
						label: `(${item.priceCode}) ${item.priceName}`,
						value: item.priceId,
						...item,
					}
				})
			} catch (error) {
				console.error(error)
				this.formItems[2].options = []
			}
		},
		// 计算追缴水量信息
		async getRecoveryInfo() {
			try {
				const { priceDetail = '--', usAmt = '--' } = await queryRecoveryInfo({
					priceId: this.formData.priceId,
					priceVersion: this.currentPriceDataComp.priceVersion,
					usAmount: this.formData.overPlanAmt,
				})
				this.$set(this.tableData[0], 'usePrice', priceDetail)
				this.$set(this.tableData[0], 'usAmt', usAmt)
			} catch (error) {
				console.error(error)
			}
		},
		// 保存
		async handleSave() {
			const valid = await this.$refs.formRef.validate()
			if (valid) {
				this.saveLoading = true
				try {
					const {
						archivesId,
						onlyRecoveryWaterFees,
						priceId,
						recordId,
						overPlanAmt: useAmount,
					} = this.formData
					await planUsageRecovery({
						archivesId,
						onlyRecoveryWaterFees,
						priceId,
						priceVersion: this.currentPriceDataComp.priceVersion,
						recordId,
						useAmount,
					})

					this.$message.success('追缴水量成功')
					this.$emit('success')
					this.isShow = false
				} catch (error) {
					console.error(error)
				} finally {
					this.saveLoading = false
				}
			}
		},
		handleOpen() {
			this.getPriceList()
		},
		handleClose() {
			this.$refs.formRef.resetFields()
			this.formData = {
				enterpriseName: '',
				enterpriseNumber: '',
				planYear: '',
				planUsageAmt: '',
				yearUsageAmt: '',

				archivesId: '',
				priceId: '',
				onlyRecoveryWaterFees: 1,
			}
			this.tableData = [{}]
			this.isShow = false
		},

		// 设置表单数据
		setFormData(data) {
			this.formData = Object.assign(this.formData, data)
			this.getArchives()
			this.$set(this.tableData[0], 'useAmount', this.formData.overPlanAmt)
		},
	},
}
</script>
<style lang="scss" scoped>
.gc-button-three {
	margin-right: 8px;
}
::v-deep {
	.el-dialog__body {
		height: 560px;
	}
	.el-select {
		width: 300px !important;
	}
	.gc-table {
		height: 240px;
	}
}

.data-box {
	.data-item {
		display: flex;
		align-items: center;
		margin-bottom: 12px;
		.label {
			width: 100px;
			margin-right: 10px;
			line-height: 1.5;
		}
		.value {
			flex: 1;
		}
	}
}
.el-tag {
	margin: 2px 0;
	& + .el-tag {
		margin-left: 4px;
	}
}
</style>
