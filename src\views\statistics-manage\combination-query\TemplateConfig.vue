<template>
	<div class="page-right template-config">
		<GcModelHeader title="新增查询模板" :icon="require('@/assets/images/icon/title-common-parameters.png')">
			<template slot="right">
				<div v-has="'cpm_report_config_add'" class="right-top">
					<el-button v-has="''" @click="handleCancel">取消</el-button>
					<el-button type="primary" v-has="''" @click="handleSubmit">保存模板</el-button>
				</div>
			</template>
		</GcModelHeader>
		<div class="model-content" v-loading="loading">
			<el-form ref="form" :model="configForm" v-bind="configFormAttrs">
				<el-form-item label="模板名称" prop="templateName">
					<el-input v-model="configForm.templateName" :readonly="updateMode" required></el-input>
				</el-form-item>
				<el-form-item label="视图选择" prop="templateView">
					<el-radio-group v-model="configForm.templateView" :readonly="updateMode" required>
						<el-radio label="meter">表卡视图</el-radio>
						<el-radio label="reading">抄表视图</el-radio>
						<el-radio label="bill">账单视图</el-radio>
						<el-radio label="payment">缴费视图</el-radio>
					</el-radio-group>
				</el-form-item>
				<template v-if="configForm.templateView">
					<el-form-item label="查询条件" prop="params">
						<ComplexSearchPanel
							ref="complexSearchPanel"
							:fields="currentParamFields"
							:data="params"
							@change="handleParamsChange"
						/>
					</el-form-item>
					<el-form-item label="输出字段" prop="columns">
						<SortPool
							ref="sortPool"
							:source="currentColumns"
							checkable
							@change="handleSortChange"
						></SortPool>
					</el-form-item>
				</template>
			</el-form>
		</div>
	</div>
</template>
<script>
import { ruleRequired } from '@/utils/rules.js'
import { addQueryTemplate, editQueryTemplate, getQueryTemplateDetail } from '@/api/statisticsManage.api'
import useParamFields from './params'
import columns from './columns'
import ComplexSearchPanel from '@/components/ComplexSearchPanel/index.vue'
import SortPool from '@/components/SortPool/index.vue'
import { getAlleyMap, getBookListNoAuth, getStaffMap } from '@/api/meterReading.api.js'
import { queryEnterprisePage } from '@/api/userManage.api'
import { apiGetMeterType } from '@/api/meterManage.api.js'
import { queryWaterNatureTree } from '@/api/basicConfig.api'

export default {
	name: 'template-config',
	components: { ComplexSearchPanel, SortPool },
	props: {
		templateId: {
			type: [String, Number],
			default: null,
		},
		updateMode: {
			type: Boolean,
			default: false,
		},
		insertMode: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			parsedParamFields: null,
			parsedColumns: null,
			configForm: {
				templateView: null,
				templateName: '',
				params: [],
				columns: [],
			},
			configFormAttrs: {
				labelWidth: '70px',
				rules: {
					templateView: [ruleRequired('请选择模板视图')],
					templateName: [ruleRequired('请输入模板名称')],
					params: [ruleRequired('请配置查询条件')],
					columns: [ruleRequired('请配置输出字段')],
				},
			},
			loading: false,
			params: [
				{ field: 'orgCode', operator: 'in', value: ['1-1-1-1-1', '1-1-1-2-1', '1-1-1-3-1'] },
				{ field: 'bookNo', operator: 'in', value: ['111000005'] },
				{ field: 'archivesIdentity', operator: '=', value: '13231313' },
				{ field: '', operator: '', value: '' },
				{ field: '', operator: '', value: '' },
			],
			columns: [
				{
					key: 'changingDate',
					name: '换表日期',
					tooltip: true,
					view: ['meter', 'reading'],
					checked: true,
					sort: 0,
				},
				{ key: 'caliber', name: '口径', tooltip: true, view: ['meter'], checked: true, sort: 1 },
				{
					key: 'meterReadingDate',
					name: '上一次抄表日期',
					tooltip: true,
					view: ['meter'],
					checked: true,
					sort: 2,
				},
				{ key: 'meterReading', name: '上次抄表读数', tooltip: true, view: ['meter'], checked: true, sort: 3 },
				{ key: 'contactPeople', name: '联系人', tooltip: true, view: ['meter'], checked: true, sort: 4 },
				{ key: 'contactPhone', name: '联系人电话', tooltip: true, view: ['meter'], checked: true, sort: 5 },
			],
		}
	},
	computed: {
		currentParamFields() {
			const view = this.configForm.templateView
			if (!view || !this.parsedParamFields) return []
			return this.parsedParamFields.filter(field => {
				return field.view.includes(view)
			})
		},
		currentColumns() {
			const view = this.configForm.templateView
			if (!view || !this.parsedColumns) return []
			return this.parsedColumns.filter(field => {
				return field.view.includes(view)
			})
		},
		isConfigStart() {
			return this.insertMode || this.updateMode
		},
	},
	watch: {
		isConfigStart: {
			immediate: true,
			handler(val) {
				if (val) {
					if (!this.parsedParamFields) {
						this.parsedParamFields = useParamFields(this)
						this.initParamsOptions()
					}
					if (!this.parsedColumns) {
						this.parsedColumns = columns.map(column => {
							return {
								...column,
								checked: true,
								sort: null,
							}
						})
					}
				}
			},
		},
		'configForm.templateView'() {
			requestAnimationFrame(() => {
				this.$refs.sortPool.setData(this.columns)
			})
		},
	},
	methods: {
		handleSortChange(list) {
			this.configForm.columns = list.map(item => {
				const { original, sort } = item
				return {
					...original,
					sort,
				}
			})
		},
		handleParamsChange(params) {
			this.configForm.params = params
		},
		handleCancel() {
			this.$emit('close')
		},
		async insertQueryTemplate() {
			this.loading = true
			await addQueryTemplate({})
			this.loading = false
			this.$emit('close')
			this.$message.success('查询模板新增成功')
		},
		async handleEdit() {
			this.loading = true
			await editQueryTemplate({
				id: this.templateId,
			})
			this.loading = false
			this.$message.success('查询模板配置更新成功')
		},
		async getQueryTemplateDetail() {
			this.loading = true
			const res = await getQueryTemplateDetail({
				id: this.templateId,
			})
			this.loading = false
			this.params = res.params
			this.columns = res.columns
		},
		async getAlleyIdList(field) {
			try {
				const res = await getAlleyMap()
				if (res) {
					field.__putOptions(
						res.map(item => {
							return {
								value: item.id,
								label: item.alleyName,
							}
						}),
					)
				}
			} catch (error) {
				console.error(error)
			}
		},
		async getBookNoList(field) {
			const res = await getBookListNoAuth({
				orgCode: '',
				alleyId: '',
			}).catch(err => {
				console.error(err)
			})
			if (res) {
				field.__putOptions(
					res.records.map(item => {
						return {
							value: item.bookNo,
							label: item.bookNo + ' ' + item.meterReadingStaffName,
						}
					}),
				)
			}
		},
		async getEnterpriseNumberList(field) {
			const res = await queryEnterprisePage({
				current: 1,
				size: 99999,
				orgCode: '',
			}).catch(err => {
				console.error(err)
			})
			if (res) {
				field.__putOptions(
					res.records.map(item => {
						return {
							value: item.enterpriseNumber,
							label: item.enterpriseNumber + ' ' + item.enterpriseName,
						}
					}),
				)
			}
		},
		async getMeterReadingStaffIdsList(field) {
			const res = await getStaffMap({
				orgCode: '',
			}).catch(err => {
				console.error(err)
			})
			if (res) {
				field.__putOptions(
					res.map(item => {
						const { staffId, staffName } = item
						return {
							value: staffId,
							label: staffName,
						}
					}),
				)
			}
		},
		async getMeterTypeIdList(field) {
			const res = await apiGetMeterType({
				tenantId: this.$store.getters.userInfo.tenantId,
			}).catch(err => {
				console.error(err)
			})
			if (res) {
				field.__putOptions(
					res.map(item => {
						return {
							value: item.meterTypeId,
							label: item.meterTypeName,
						}
					}),
				)
			}
		},
		async getPriceNatureIdList(field) {
			const res = await queryWaterNatureTree().catch(err => {
				console.error(err)
			})
			if (res) {
				field.__putOptions(res)
			}
		},
		initParamsOptions() {
			this.loading = true
			const dynamicParamFields = this.parsedParamFields.filter(field => typeof field.__putOptions === 'function')
			const dynamicFetchList = []
			for (const field of dynamicParamFields) {
				let key = field.key
				key = key[0].toUpperCase() + key.substr(1)
				const funcName = `get${key}List`
				const func = this[funcName]
				if (typeof func === 'function') {
					dynamicFetchList.push(func.call(this, field))
				} else {
					console.warn('复合查询条件动态选项查询函数未定义: ' + funcName)
				}
			}
			Promise.all(dynamicFetchList)
				.catch(err => {
					console.error(err)
				})
				.finally(() => {
					this.loading = false
				})
		},
		handleSubmit() {
			this.$refs.form
				.validate()
				.then(() => {
					const api = this.insertMode ? this.insertQueryTemplate : this.handleEdit
					api.call(this)
				})
				.catch(() => {})
		},
	},
}
</script>

<style lang="scss" scoped>
.template-config {
	padding: 0;
	.model-header {
		height: 70px;
	}
	.model-content {
		flex-grow: 1;
		padding: 0 20px 20px 30px;
		::v-deep {
			.el-form-item__label {
				text-align: left;
				padding-right: 10px;
			}
		}
	}
	.complex-search-title {
		font-size: 14px;
		font-family: SourceHanSansCN-Regular, SourceHanSansCN;
		font-weight: 400;
		color: #4e4e4e;
	}
	::v-deep {
		.el-form-item {
			margin-bottom: 15px;
		}
	}
}
</style>
