<template>
	<div class="page-right template-config">
		<GcModelHeader :title="title" :icon="require('@/assets/images/icon/title-common-parameters.png')">
			<template slot="right">
				<div v-has="'cpm_report_config_add'" class="right-top">
					<el-button v-has="''" @click="handleCancel">取消</el-button>
					<el-button type="primary" v-has="''" @click="handleSubmit">保存模板</el-button>
				</div>
			</template>
		</GcModelHeader>
		<div class="model-content" v-loading="loading">
			<el-form ref="form" :model="configForm" v-bind="configFormAttrs">
				<el-form-item label="模板名称" prop="templateName">
					<el-input v-model="configForm.templateName" :readonly="updateMode" required></el-input>
				</el-form-item>
				<el-form-item label="视图选择" prop="templateView">
					<el-radio-group v-model="configForm.templateView" :readonly="updateMode" required>
						<el-radio label="meter">表卡视图</el-radio>
						<el-radio label="reading">抄表视图</el-radio>
						<el-radio label="bill">账单视图</el-radio>
						<el-radio label="payment">缴费视图</el-radio>
					</el-radio-group>
				</el-form-item>
				<template v-if="configForm.templateView">
					<el-form-item label="查询条件" prop="params">
						<ComplexSearchPanel
							ref="complexSearchPanel"
							:fields="currentParamFields"
							:data="params"
							@change="handleParamsChange"
						/>
					</el-form-item>
					<el-form-item label="输出字段" prop="columns">
						<SortPool
							ref="sortPool"
							:source="currentColumns"
							checkable
							@change="handleSortChange"
						></SortPool>
					</el-form-item>
				</template>
			</el-form>
		</div>
	</div>
</template>
<script>
import { ruleRequired } from '@/utils/rules.js'
import { updateQueryTemplate, addQueryTemplate } from '@/api/statisticsManage.api'
import useParamFields, { VIEW_CONFIGS, FIELD_CHANGE_CONFIG } from './params'
import columnsTemplate from './columns'
import ComplexSearchPanel from '@/components/ComplexSearchPanel/index.vue'
import SortPool from '@/components/SortPool/index.vue'
import { getAlleyMap, getBookListNoAuth, getStaffMap } from '@/api/meterReading.api.js'
import { queryEnterprisePage } from '@/api/userManage.api'
import { apiGetMeterType } from '@/api/meterManage.api.js'
import { queryWaterNatureTree } from '@/api/basicConfig.api'
import { isBlank } from '@/utils/validate'

const TEMPLATE_VIEW_MAP = {
	meter: 1,
	reading: 2,
	bill: 3,
	payment: 4,
	1: 'meter',
	2: 'reading',
	3: 'bill',
	4: 'payment',
}

const vaildateParamsField = (rules, data) => {
	rules = rules || []
	data = data || []
	const keys = data.map(item => {
		return item.key || ''
	})
	const errors = []
	rules.forEach(rule => {
		const { type, key, message } = rule
		if (type === 'required') {
			if (!keys.includes(key)) {
				errors.push(message || `请配置${key}`)
			}
			return
		}

		if (type === 'required_one_of') {
			const hasKey = (key || []).some(item => {
				return keys.includes(item)
			})

			if (!hasKey) {
				errors.push(message || `请配置以下字段之一：${key.join('、')}`)
			}

			return
		}
	})

	return {
		isValid: errors.length === 0,
		errors,
	}
}

const parseToArray = str => {
	try {
		return JSON.parse(str || '{}')
	} catch (e) {
		console.log(e)
		return []
	}
}

export default {
	name: 'template-config',
	components: { ComplexSearchPanel, SortPool },
	props: {
		data: {
			type: Object,
			default() {
				return {}
			},
		},
		updateMode: {
			type: Boolean,
			default: false,
		},
		insertMode: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			parsedParamFields: null,
			parsedColumns: null,
			configForm: {
				templateView: null,
				templateName: '',
				params: [],
				columns: [],
			},
			configFormAttrs: {
				labelWidth: '70px',
				rules: {
					templateView: [ruleRequired('请选择模板视图')],
					templateName: [ruleRequired('请输入模板名称')],
					params: [
						ruleRequired('请配置查询条件'),
						{
							validator: (rule, data, callback) => {
								if (!data || data.length === 0) {
									return callback(new Error('请配置查询条件'))
								}

								for (let i = 0; i < data.length; i++) {
									const item = data[i]
									const { value, operator, key } = item

									if (isBlank(value) || isBlank(operator) || isBlank(key)) {
										return callback(new Error('请配置完成查询条件'))
									}
								}

								callback()
							},
						},
						{
							validator: (rule, data, callback) => {
								const { viewConfig } = this

								let { validationRules } = viewConfig || {}
								validationRules = validationRules || []
								const { isValid, errors } = vaildateParamsField(validationRules, data)

								if (isValid) {
									callback()
								} else {
									callback(new Error(errors.join(';')))
								}
							},
						},
					],
					columns: [ruleRequired('请配置输出字段')],
				},
			},
			loading: false,
			params: [{ field: '', operator: '', value: '' }],
			columns: [],
			isInit: false,
			changeConfigs: [],
		}
	},
	computed: {
		viewConfig() {
			const { configForm } = this
			const { templateView } = configForm || {}
			return VIEW_CONFIGS[templateView] || {}
		},
		currentParamFields() {
			const { configForm, parsedParamFields } = this
			const { templateView, params } = configForm || {}
			if (!templateView || !parsedParamFields) return []

			let onlyOneFields = {}
			let onlyOneFieldKeys = []

			const fields = parsedParamFields.reduce((acc, field) => {
				let { view, onlyOne, key } = field
				view = view || []
				if (view.includes(templateView)) {
					let newFieldItem = {
						...field,
					}
					if (onlyOne) {
						onlyOneFields[key] = newFieldItem
						onlyOneFieldKeys.push(key)
					}
					acc.push(newFieldItem)
				}

				return acc
			}, [])

			if (onlyOneFieldKeys.length) {
				const paramsKeys = params.map(item => {
					return item.key
				})

				onlyOneFieldKeys.map(key => {
					if (paramsKeys.includes(key)) {
						onlyOneFields[key].disabled = true
					}
				})
			}

			return fields
		},
		currentColumns() {
			const view = this.configForm.templateView
			if (!view || !this.parsedColumns) return []
			return this.parsedColumns.filter(field => {
				return field.view.includes(view)
			})
		},
		isConfigStart() {
			return this.insertMode || this.updateMode
		},
		title() {
			const { insertMode } = this

			return insertMode ? '新增查询模板' : '修改查询模板'
		},
	},
	watch: {
		isConfigStart: {
			immediate: true,
			handler(val) {
				if (val) {
					if (!this.parsedParamFields) {
						this.parsedParamFields = useParamFields(this)
						this.initParamsOptions()
					}
					if (!this.parsedColumns) {
						this.parsedColumns = columnsTemplate.map(column => {
							return {
								...column,
								checked: false,
								sort: null,
							}
						})
					}

					this.initData()
				}
			},
		},
		'configForm.templateView'() {
			const { viewConfig } = this

			const { defaultParams } = viewConfig || {}
			const params = defaultParams ? defaultParams() : [{ field: '', operator: '', value: '' }]
			this.configForm.params = params
			this.params = params
			this.columns = []
			this.configForm.columns = []
			requestAnimationFrame(() => {
				this.$refs.sortPool.setData(this.columns)
			})
		},
		'configForm.params': {
			handler(current) {
				const getMap = arr => {
					return arr.reduce((acc, item) => {
						const { key, value } = item
						acc[key] = {
							value,
							item,
						}
						return acc
					}, {})
				}
				let { parsedParamFields, isInit, changeConfigs } = this
				parsedParamFields = parsedParamFields || []
				const currentMap = getMap(current || [])
				const currentParamFieldsMap = parsedParamFields.reduce((acc, item) => {
					const { key } = item
					acc[key] = item
					return acc
				}, {})
				const params = (current || []).reduce((acc, item) => {
					const { key, value } = item
					acc[key] = value
					return acc
				}, {})
				changeConfigs.forEach(config => {
					const { source, target } = config

					const currentSouce = currentMap[source]
					const currentTarget = currentMap[target]

					if (!currentSouce) {
						return
					}

					const { value } = currentSouce
					// TODO 后期需加上数组内值对比
					const isChanged = value !== config.prev
					config.prev = value
					if (isChanged) {
						const field = currentParamFieldsMap[target]

						if (currentTarget && isInit === true) {
							currentTarget.item.value = ''
						}

						if (field) {
							this.updateParamsFieldOptions(field, params)
						}
					}
				})

				this.isInit = true
			},
			deep: true,
			immediate: true,
		},
	},
	methods: {
		handleSortChange(list) {
			this.configForm.columns = list.map(item => {
				const { original, sort } = item
				return {
					...original,
					sort,
				}
			})
		},
		handleParamsChange(params) {
			this.configForm.params = params
		},
		handleCancel() {
			this.$emit('close')
		},

		async addOrUpdateQueryTemplate() {
			try {
				const { updateMode, configForm } = this
				this.loading = true
				const params = this.formatRequestData()

				let apiFunc = addQueryTemplate
				if (updateMode) {
					apiFunc = updateQueryTemplate
					params.id = configForm.id
				}

				await apiFunc(params)
				this.$emit('refresh')
				this.$message.success(`${this.title}成功`)
			} catch (e) {
				console.log(e)
			} finally {
				this.loading = false
			}
		},
		async getAlleyIdList(field, params) {
			let { org_code } = params || {}
			if (!org_code) {
				return
			}
			try {
				const res = await getAlleyMap({
					orgCode: org_code || '',
				})
				if (res) {
					field.__putOptions(
						res.map(item => {
							return {
								value: item.alleyCode,
								label: item.alleyName,
							}
						}),
					)
				}
			} catch (error) {
				console.error(error)
			}
		},
		async getBookNoList(field, params) {
			let { org_code } = params || {}
			if (!org_code) {
				return
			}

			const res = await getBookListNoAuth({
				orgCode: org_code || '',
				alleyId: '',
				size: 999,
			}).catch(err => {
				console.error(err)
			})
			if (res) {
				field.__putOptions(
					res.records.map(item => {
						return {
							value: item.bookNo,
							label: item.bookNo + ' ' + item.meterReadingStaffName,
						}
					}),
				)
			}
		},
		async getEnterpriseNumberList(field) {
			const res = await queryEnterprisePage({
				current: 1,
				size: 99999,
				orgCode: '',
			}).catch(err => {
				console.error(err)
			})
			if (res) {
				field.__putOptions(
					res.records.map(item => {
						return {
							value: item.enterpriseNumber,
							label: item.enterpriseNumber + ' ' + item.enterpriseName,
						}
					}),
				)
			}
		},
		async getMeterReadingStaffIdsList(field, params) {
			let { org_code } = params || {}
			if (!org_code) {
				return
			}

			const res = await getStaffMap({
				orgCode: org_code,
			}).catch(err => {
				console.error(err)
			})
			if (res) {
				field.__putOptions(
					res.map(item => {
						const { staffId, staffName } = item
						return {
							value: staffId,
							label: staffName,
						}
					}),
				)
			}
		},
		async getMeterTypeIdList(field) {
			const res = await apiGetMeterType({
				tenantId: this.$store.getters.userInfo.tenantId,
			}).catch(err => {
				console.error(err)
			})
			if (res) {
				field.__putOptions(
					res.map(item => {
						return {
							value: item.meterTypeId,
							label: item.meterTypeName,
						}
					}),
				)
			}
		},
		async getPriceNatureIdList(field) {
			const res = await queryWaterNatureTree().catch(err => {
				console.error(err)
			})
			if (res) {
				field.__putOptions(res)
			}
		},
		initParamsOptions() {
			this.loading = true
			const dynamicParamFields = this.parsedParamFields.filter(field => field.optionMethod)
			const dynamicFetchList = []
			for (const field of dynamicParamFields) {
				const func = this.updateParamsFieldOptions(field)

				if (func) {
					dynamicFetchList.push(func)
				}
			}
			Promise.all(dynamicFetchList)
				.catch(err => {
					console.error(err)
				})
				.finally(() => {
					this.loading = false
				})
		},
		updateParamsFieldOptions(field, params = {}) {
			let key = field.optionMethod
			key = key[0].toUpperCase() + key.substr(1)
			const funcName = `get${key}List`
			const func = this[funcName]
			if (typeof func === 'function') {
				return func.call(this, field, params)
			} else {
				console.warn('复合查询条件动态选项查询函数未定义: ' + funcName)

				return null
			}
		},
		handleSubmit() {
			this.$refs.form
				.validate()
				.then(() => {
					this.addOrUpdateQueryTemplate()
				})
				.catch(() => {})
		},
		formatRequestData() {
			const { configForm, parsedParamFields } = this
			let { columns, params, templateName, templateView } = configForm
			const COLUMNS_MAP = parsedParamFields.reduce((acc, item) => {
				const { key } = item
				acc[key] = item
				return acc
			}, {})
			columns = columns || []
			params = params || []

			const templateDisplay = columns.map(item => {
				const { key, name, sort } = item

				return {
					key,
					name,
					sort,
				}
			})

			const templateCondition = params.map(item => {
				const { key, operator, value } = item
				const config = COLUMNS_MAP[key] || {
					dataType: 'String',
					dataFormat: 'Object',
				}

				const { dataType, dataFormat } = config

				return {
					key,
					operator,
					value,
					dataType,
					dataFormat,
				}
			})

			const templateType = TEMPLATE_VIEW_MAP[templateView]

			return {
				templateDisplay,
				templateCondition,
				templateName,
				templateType,
			}
		},
		initData() {
			const { data, updateMode } = this
			this.changeConfigs = FIELD_CHANGE_CONFIG.map(item => {
				return {
					...item,
				}
			})
			if (updateMode) {
				const { templateCondition, templateDisplay, templateName, templateType, id } = data
				const params = parseToArray(templateCondition)
				const columns = parseToArray(templateDisplay)
				const templateView = TEMPLATE_VIEW_MAP[templateType] || ''
				this.configForm.params = params
				this.configForm.columns = columns
				this.configForm.templateView = templateView
				this.configForm.id = id
				this.configForm.templateName = templateName
				this.columns = columns
				this.params = params

				this.$nextTick(() => {
					this.$refs.sortPool.setData(this.columns)
				})
			} else {
				this.configForm.params = []
				this.configForm.columns = []
				this.configForm.templateView = []
				this.configForm.id = ''
				this.configForm.templateName = ''
				this.columns = []
				this.params = []
				this.$nextTick(() => {
					this.$refs.sortPool.setData([])
				})
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.template-config {
	padding: 0;

	.model-header {
		height: 70px;
	}

	.model-content {
		flex-grow: 1;
		padding: 0 20px 20px 30px;
		overflow: auto;

		::v-deep {
			.el-form-item__label {
				text-align: left;
				padding-right: 10px;
			}
		}
	}

	.complex-search-title {
		font-size: 14px;
		font-family: SourceHanSansCN-Regular, SourceHanSansCN;
		font-weight: 400;
		color: #4e4e4e;
	}

	::v-deep {
		.el-form-item {
			margin-bottom: 15px;
		}
	}
}
</style>
