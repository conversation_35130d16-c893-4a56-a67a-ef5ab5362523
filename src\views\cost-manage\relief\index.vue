<template>
	<div class="wrapper">
		<div class="page-layout">
			<div class="page-left">
				<GcModelHeader
					class="info-title"
					title="账单信息"
					:icon="require('@/assets/images/icon/title-common-parameters.png')"
				></GcModelHeader>
				<div class="form-container">
					<div class="form-item" v-for="(item, index) in formList" :key="index">
						<div class="label">{{ item.label }}</div>
						<div class="value">{{ item.value }}</div>
					</div>
				</div>
			</div>
			<div class="page-right">
				<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
					<template v-slot:adjustLastMeterReading>
						<el-form-item v-if="formData.type === '1'" label="是否调整指针数" prop="adjustLastMeterReading">
							<el-checkbox
								v-model="formData.adjustLastMeterReading"
								:disabled="detailData && !detailData.meterId"
								size="mini"
							>
								调整指针
							</el-checkbox>
						</el-form-item>
					</template>
					<template v-slot:changes>
						<el-form-item
							v-if="['1', '3'].includes(formData.type)"
							:label="formData.type === '1' ? '减免水量' : '污水费用'"
							prop="useAmount"
							style="margin-right: 6px; margin-bottom: 0"
						>
							<el-input v-model="formData.useAmount" placeholder="请输入"></el-input>
						</el-form-item>
						<el-form-item
							v-else-if="formData.type === '2'"
							label="价格"
							prop="priceId"
							style="margin-right: 6px; height: 30px"
						>
							<el-select v-model="formData.priceId" placeholder="请选择" clearable filterable>
								<el-option
									v-for="(item, index) in priceList"
									:label="item.label"
									:value="item.value"
									:key="index"
								></el-option>
							</el-select>
						</el-form-item>
						<el-form-item
							v-show="['1', '3'].includes(formData.type)"
							:label="totalValue"
							style="margin-bottom: 0"
						></el-form-item>
					</template>
					<el-button type="primary" @click="handleReduction">减免计算</el-button>
				</GcFormSimple>
				<div class="table-container">
					<div class="table">
						<div class="flex">
							<h5>减免前账单</h5>
							<div class="total-container flex" v-show="this.tableData0.length">
								<div>合计: 水量{{ getTotalValue(this.tableData0).useAmount }}</div>
								<div>金额 {{ getTotalValue(this.tableData0).useAmt }}</div>
							</div>
						</div>
						<GcTable :columns="columns" :table-data="tableData0" />
					</div>
					<div class="table">
						<div class="flex">
							<h5>减免信息</h5>
							<div class="total-container flex" v-show="this.tableData1.length">
								<div>合计: 水量{{ getTotalValue(this.tableData1).useAmount }}</div>
								<div>金额 {{ getTotalValue(this.tableData1).useAmt }}</div>
							</div>
						</div>
						<GcTable :columns="columns" :table-data="tableData1" />
					</div>
					<div class="table">
						<div class="flex">
							<h5>减免后账单</h5>
							<div class="total-container flex" v-show="this.tableData2.length">
								<div>合计: 水量{{ getTotalValue(this.tableData2).useAmount }}</div>
								<div>金额 {{ getTotalValue(this.tableData2).useAmt }}</div>
							</div>
						</div>
						<GcTable :columns="columns" :table-data="tableData2" />
					</div>
				</div>
			</div>
		</div>

		<div class="button-group">
			<el-button class="btn-preview" @click="handleCancel">取消</el-button>
			<el-button class="btn-create" type="primary" :disabled="!tableData2.length" @click="handleSubmit">
				保存并缴费
			</el-button>
		</div>
	</div>
</template>

<script>
import { ruleRequired, RULE_FEES, RULE_POSITIVEINTEGERONLY_STARTOFZERO } from '@/utils/rules.js'
import { removeNullParams, trimParams } from '@/utils/index.js'
import { accMul, accSub, accAdd, accDiv } from '@/utils/calc.js'
import { isBlank } from '@/utils/validate.js'
import { priceLevelEnum } from '@/consts/enums.js'
import { apiGetBillDetail, apiBillAdjustReduction } from '@/api/costManage.api.js'
import { apiEffectivePrice } from '@/api/meterManage.api.js'

export default {
	data() {
		return {
			formData: {
				type: '1',
				reason: '',
				useAmount: '',
				adjustLastMeterReading: false,
				priceId: '',
			},
			formItems: [
				{
					type: 'el-radio',
					label: '减免方式',
					prop: 'type',
					options: [
						{
							label: '按水量',
							value: '1',
						},
						{
							label: '按水价',
							value: '2',
						},
						{
							label: '污水费',
							value: '3',
							disabled: true,
						},
					],
					events: {
						change: this.handleChangeType,
					},
				},
				{
					type: 'el-select',
					label: '原因',
					prop: 'reason',
					options:
						this.$store.getters?.dataList?.billAdjustReasonType?.map(item => {
							return {
								label: item.sortName,
								value: item.sortName,
							}
						}) || [],
				},
				{
					type: 'slot',
					slotName: 'changes',
				},
				{
					type: 'slot',
					slotName: 'adjustLastMeterReading',
					prop: 'adjustLastMeterReading',
				},
			],
			detailData: {},
			columns: [
				{
					key: 'itemName',
					name: '费用名称',
					tooltip: true,
				},
				{
					key: 'priceLevel',
					name: '阶梯',
					tooltip: true,
					render: (h, row, total, scope) => {
						return h('span', {}, priceLevelEnum[row[scope.column.property]])
					},
				},
				{
					key: 'usePrice',
					name: '价格',
					tooltip: true,
				},
				{
					key: 'useAmount',
					name: '水量',
					tooltip: true,
				},
				{
					key: 'useAmt',
					name: '金额',
					tooltip: true,
				},
			],
			priceList: [],
			tableData1: [],
			tableData2: [],
		}
	},
	computed: {
		formAttrs() {
			const rules = {
				useAmount: [
					ruleRequired('必填'),
					this.formData.type == 1 ? RULE_POSITIVEINTEGERONLY_STARTOFZERO : RULE_FEES,
					{ validator: this.validateMaxValue, trigger: 'blur' },
				],
				priceId: [ruleRequired('请选择价格')],
			}

			return {
				inline: true,
				rules,
			}
		},
		formList() {
			const list = [
				{
					label: '账单编号',
					value: this.detailData?.billNo || '--',
				},
				{
					label: '价格编号',
					value: this.detailData?.priceCode || '--',
				},
				{
					label: '用水性质',
					value: this.detailData?.natureName || '--',
				},
				{
					label: '表卡编号',
					value: this.detailData?.archivesIdentity || '--',
				},
				{
					label: '当前指针数',
					value: this.detailData?.meterReading || '--',
				},
				{
					label: '用户名称',
					value: this.detailData?.userName || '--',
				},
				{
					label: '年累计量',
					value: this.detailData?.yearAmount || '--',
				},
				{
					label: '地址',
					value: this.detailData?.addressFullName || '--',
				},
				{
					label: '账单抄表年月',
					value: this.detailData?.meterReadingYearMonth || '--',
				},
				{
					label: '是否免污水表卡',
					value: isBlank(this.detailData?.exemptSewage) ? '--' : this.detailData?.exemptSewage ? '是' : '否',
				},
			]
			return list
		},
		tableData0() {
			return this.detailData.billDetailList || []
		},
		totalValue() {
			const { type } = this.formData
			const { useAmount, billDetailList } = this.detailData
			let value = ''

			if (type === '1') {
				return isBlank(useAmount) ? '' : `<=${useAmount}`
			} else if (type === '3') {
				const obj = billDetailList.find(item => item.itemId == 10001)
				if (obj) {
					value = isBlank(obj.useAmt) ? '' : `<=${obj.useAmt}`
				}
			}
			return value
		},
		maxValue() {
			const { type } = this.formData
			const { useAmount, billDetailList } = this.detailData
			let value = ''
			if (type === '3') {
				const obj = billDetailList.find(item => item.itemId == 10001)
				if (obj) {
					value = isBlank(obj.useAmt) ? 0 : obj.useAmt
				}
			} else {
				value = isBlank(useAmount) ? 0 : useAmount
			}

			return value
		},
	},
	activated() {
		const { id, year } = this.$route.query
		if (id) {
			this._apiGetBillDetail({ billId: id, year })
			this._apiEffectivePrice()
		}
	},
	methods: {
		async _apiEffectivePrice() {
			const data = await apiEffectivePrice()
			if (data && data.length > 0) {
				this.priceList = data.map(item => {
					return {
						label: item.priceCode + ' (' + item.priceName + ')',
						value: item.priceId,
						...item,
					}
				})
			}
		},
		async _apiGetBillDetail(params) {
			const res = await apiGetBillDetail(params)
			this.detailData = res
			// 有污水费用 可选择污水费减免
			const obj = res.billDetailList.find(item => item.itemId == 10001)
			const shouldEnableOption = obj && obj.useAmt > 0
			if (shouldEnableOption) {
				this.formItems[0].options[2].disabled = false
			}
		},
		// 设置最大值校验
		validateMaxValue(rule, value, callback) {
			const maxValue = this.maxValue // 动态获取最大值
			if (value > maxValue) {
				callback(new Error(`输入值不能超过${maxValue}`))
			} else {
				callback()
			}
		},
		// 减免计算
		async handleReduction() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			// 按水量
			if (this.formData.type === '1') {
				this.tableData2 = this._.cloneDeep(this.tableData0)
				this.tableData1 = this._.cloneDeep(this.tableData0)
				let accSubValue = this.formData.useAmount
				// 减免后账单
				const arr = [...this.tableData2].reverse()
				arr.forEach(item => {
					// 如果污水费用为0，直接跳过
					if (item.itemId === 10001 && item.useAmt === 0) {
						return
					}
					if (item.priceLevel) {
						const subResult = accSub(item.useAmount, accSubValue)
						if (subResult < 0) {
							accSubValue = Math.abs(subResult)
							item.useAmount = 0
						} else {
							item.useAmount = subResult
							accSubValue = 0 // 终止后续计算
						}
					} else {
						item.useAmount = accSub(item.useAmount, accSubValue)
					}
					item.useAmt = accMul(item.useAmount, item.usePrice)
				})
				this.tableData2 = arr.reverse()
				// 减免信息
				this.tableData1 = this.tableData1.map((item, index) => {
					item.useAmount = accSub(this.tableData0[index].useAmount, this.tableData2[index].useAmount)
					item.useAmt = accSub(this.tableData0[index].useAmt, this.tableData2[index].useAmt)
					return item
				})
			}
			// 按水价
			if (this.formData.type === '2') {
				const priceObj = this.priceList.find(item => item.priceId === this.formData.priceId)
				// 阶梯价格
				if (priceObj.levelPrice) {
					this.tableData2 = []
					this.tableData1 = []
					let accSubValue = this.maxValue
					const levelBorderArr = priceObj.levelBorder.split('|')
					const levelPriceArr = priceObj.levelPrice.split('|')
					const table2 = levelBorderArr.map((value, index) => {
						const obj = {
							billId: this.$route.query.id,
							priceLevel: index + 1,
							itemId: 11,
							itemName: '纯水费',
							usePrice: '',
							useAmount: '',
							useAmt: '',
						}
						if (accSub(accSubValue, value) > 0) {
							accSubValue = accSub(accSubValue, value)
							obj.useAmount = value
						} else {
							obj.useAmount = accSubValue
							accSubValue = 0 // 终止后续计算
						}

						obj.usePrice = levelPriceArr[index]
						obj.useAmt = accMul(obj.useAmount, obj.usePrice)
						return obj
					})
					// 纯水费总金额减免前
					let waterFeeTotalBefore = this.tableData0
						.filter(item => item.itemId === 11)
						.map(item => item.useAmt)
						.reduce((acc, curr) => accAdd(acc, curr), 0)
					// 纯水费总金额减免后
					let waterFeeTotalAfter = table2.map(item => item.useAmt).reduce((acc, curr) => accAdd(acc, curr), 0)
					this.tableData1 = [
						{
							billId: this.$route.query.id,
							priceLevel: null,
							itemId: 11,
							itemName: '纯水费',
							usePrice: '--',
							useAmount: this.maxValue,
							useAmt: accSub(waterFeeTotalBefore, waterFeeTotalAfter),
						},
					]
					if (priceObj.priceBillItemList.length > 0) {
						priceObj.priceBillItemList.map(item => {
							const foundItem = this.tableData0.find(i => i.itemId === item.billItemId)
							const otherFeeTotal = foundItem ? foundItem.useAmt : 0
							const otherFeeObj = {
								billId: this.$route.query.id,
								priceLevel: null,
								itemId: item.billItemId,
								itemName: item.itemName,
								usePrice: item.billItemPrice,
								useAmount: this.maxValue,
								useAmt: accMul(this.maxValue, item.billItemPrice),
							}
							// 是免污水：污水费直接减免
							if (item.billItemId === 10001 && this.detailData.exemptSewage) {
								otherFeeObj.useAmt = 0
							}
							table2.push(otherFeeObj)
							if (foundItem) {
								const newOtherFeeObj = Object.assign({}, otherFeeObj, {
									useAmt: accSub(otherFeeTotal, otherFeeObj.useAmt),
									usePrice: '--',
								})
								this.tableData1.push(newOtherFeeObj)
							}
						})
						// 减免前账单中的附加费没有和新水价中的附加费匹配上
						const unmatchedItems = this.tableData0.filter(i => {
							return (
								!priceObj.priceBillItemList.some(item => item.billItemId === i.itemId) &&
								i.itemId !== 11
							)
						})
						unmatchedItems.map(item => {
							const newOtherFeeObj = {
								billId: this.$route.query.id,
								priceLevel: null,
								itemId: item.itemId,
								itemName: item.itemName,
								usePrice: '--',
								useAmount: this.maxValue,
								useAmt: item.useAmt, // 金额展示为负数
							}
							this.tableData1.push(newOtherFeeObj)
						})
					} else {
						let otherFeeArr = this.tableData0.filter(item => item.itemId !== 11).map(item => ({ ...item }))
						otherFeeArr = otherFeeArr.map(item => {
							item.usePrice = '--'
							return item
						})
						this.tableData1.push(...otherFeeArr)
					}
					this.tableData2 = table2
				} else {
					// 非阶梯价格
					const table2 = [
						{
							billId: this.$route.query.id,
							priceLevel: null,
							itemId: 11,
							itemName: '纯水费',
							usePrice: priceObj.singlePrice,
							useAmount: this.maxValue,
							useAmt: accMul(priceObj.singlePrice, this.maxValue),
						},
					]
					// 纯水费总金额减免前
					let waterFeeTotalBefore = this.tableData0
						.filter(item => item.itemId === 11)
						.map(item => item.useAmt)
						.reduce((acc, curr) => accAdd(acc, curr), 0)
					// 纯水费总金额减免后
					let waterFeeTotalAfter = table2[0].useAmt
					this.tableData1 = [
						{
							billId: this.$route.query.id,
							priceLevel: null,
							itemId: 11,
							itemName: '纯水费',
							usePrice: '--',
							useAmount: this.maxValue,
							useAmt: accSub(waterFeeTotalBefore, waterFeeTotalAfter),
						},
					]
					if (priceObj.priceBillItemList.length > 0) {
						priceObj.priceBillItemList.map(item => {
							const foundItem = this.tableData0.find(i => i.itemId === item.billItemId)
							const otherFeeTotal = foundItem ? foundItem.useAmt : 0
							const otherFeeObj = {
								billId: this.$route.query.id,
								priceLevel: null,
								itemId: item.billItemId,
								itemName: item.itemName,
								usePrice: item.billItemPrice,
								useAmount: this.maxValue,
								useAmt: accMul(this.maxValue, item.billItemPrice),
							}
							// 是免污水：污水费直接减免
							if (item.billItemId === 10001 && this.detailData.exemptSewage) {
								otherFeeObj.useAmt = 0
							}
							table2.push(otherFeeObj)
							if (foundItem) {
								const newOtherFeeObj = Object.assign({}, otherFeeObj, {
									useAmt: accSub(otherFeeTotal, otherFeeObj.useAmt),
									usePrice: '--',
								})
								this.tableData1.push(newOtherFeeObj)
							}
						})
						// 减免前账单中的附加费没有和新水价中的附加费匹配上
						const unmatchedItems = this.tableData0.filter(i => {
							return (
								!priceObj.priceBillItemList.some(item => item.billItemId === i.itemId) &&
								i.itemId !== 11
							)
						})
						unmatchedItems.map(item => {
							const newOtherFeeObj = {
								billId: this.$route.query.id,
								priceLevel: null,
								itemId: item.itemId,
								itemName: item.itemName,
								usePrice: '--',
								useAmount: this.maxValue,
								useAmt: item.useAmt,
							}
							this.tableData1.push(newOtherFeeObj)
						})
					} else {
						// 减免账单前的 其它费用数据
						let otherFeeArr = this.tableData0.filter(item => item.itemId !== 11).map(item => ({ ...item }))
						otherFeeArr = otherFeeArr.map(item => {
							item.usePrice = '--'
							return item
						})
						this.tableData1.push(...otherFeeArr)
					}
					this.tableData2 = table2
				}
			}
			// 按污水费
			if (this.formData.type === '3') {
				this.tableData2 = this._.cloneDeep(this.tableData0)
				this.tableData1 = this._.cloneDeep(this.tableData0)
				// 减免后账单
				this.tableData2 = this.tableData2.map(item => {
					if (item.itemId === 10001) {
						item.useAmt = accSub(item.useAmt, this.formData.useAmount)
						item.usePrice =
							accDiv(item.useAmt, item.useAmount) > 0
								? accDiv(item.useAmt, item.useAmount).toFixed(4)
								: accDiv(item.useAmt, item.useAmount)
					}
					return item
				})
				// 减免信息
				this.tableData1 = this.tableData1.map((item, index) => {
					item.useAmt = accSub(this.tableData0[index].useAmt, this.tableData2[index].useAmt)
					if (item.itemId === 10001) {
						item.usePrice =
							accDiv(item.useAmt, item.useAmount) > 0
								? accDiv(item.useAmt, item.useAmount).toFixed(4)
								: accDiv(item.useAmt, item.useAmount)
					}

					return item
				})
			}
			this.$message({
				message: '计算成功',
				type: 'success',
				duration: '1500',
			})
		},
		// 改变减免方式
		handleChangeType() {
			this.$refs.formRef.clearValidate()
			this.formData.useAmount = ''
			this.formData.priceId = ''
			this.tableData1 = []
			this.tableData2 = []
		},
		getTotalValue(arr) {
			const useAmount = arr
				.filter(item => item.itemId === 11)
				.map(item => item.useAmount)
				.reduce((acc, curr) => accAdd(acc, curr), 0)
			const useAmt = arr.map(item => item.useAmt).reduce((acc, curr) => accAdd(acc, curr), 0)
			return {
				useAmount,
				useAmt,
			}
		},
		async handleSubmit() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			const formParams = trimParams(removeNullParams(this.formData))
			const { id, year } = this.$route.query
			// 纯水费用水量
			const useAmount = this.tableData2
				.filter(item => item.itemId === 11)
				.map(item => item.useAmount)
				.reduce((acc, curr) => accAdd(acc, curr), 0)
			// 纯水费用水金额
			const useAmt = this.tableData2
				.filter(item => item.itemId === 11)
				.map(item => item.useAmt)
				.reduce((acc, curr) => accAdd(acc, curr), 0)
				.toFixed(2)
			// 附加费金额
			const billItemAmt = this.tableData2
				.filter(item => item.itemId !== 11)
				.map(item => item.useAmt)
				.reduce((acc, curr) => accAdd(acc, curr), 0)
				.toFixed(2)
			// 应收金额
			const receivableAmount = accAdd(useAmt, billItemAmt).toFixed(2)
			// 价格版本
			if (this.formData.type === '2') {
				const priceItem = this.priceList.find(item => item.priceId === this.formData.priceId)
				formParams.priceVersion = priceItem.priceVersion
				formParams.priceCode = priceItem.priceCode
				formParams.priceId = this.formData.priceId
				delete formParams.adjustLastMeterReading
			} else {
				formParams.priceVersion = this.detailData.priceVersion
				formParams.priceId = this.detailData.priceId
				formParams.priceCode = this.detailData.priceCode
				formParams.adjustInput = this.formData.useAmount
				if (this.formData.type === '1') {
					formParams.adjustLastMeterReading = formParams.adjustLastMeterReading ? 1 : 0
				} else {
					delete formParams.adjustLastMeterReading
				}
			}
			Object.assign(formParams, {
				billDetailList: this.tableData2,
				billId: id,
				year,
				billItemAmt,
				receivableAmount,
				useAmt,
				useAmount,
			})

			const res = await apiBillAdjustReduction(formParams)
			if (this.detailData.billStatus == 0) {
				this.$alert('已成功调整，当前账单未开帐无法缴费', '提示', {
					confirmButtonText: '确定',
					callback: () => {
						this.$store.dispatch('tagsView/delView', this.$route).then(() => {
							this.$router.push({
								path: '/costManage/billManage',
							})
						})
					},
				})
			} else {
				this.$message.success('调整成功')
				this.$store.dispatch('tagsView/delView', this.$route).then(() => {
					this.$router.push({
						path: '/costManage/paymentPage',
						query: {
							ids: res,
							year,
						},
					})
				})
			}
		},
		handleCancel() {
			this.$store.dispatch('tagsView/delView', this.$route).then(tags => {
				const { fullPath } = tags.slice(-1)[0]
				this.$router.push(fullPath || '/')
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.wrapper {
	display: flex;
	flex-direction: column;
	height: 100%;
}
.flex {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.total-container {
	gap: 18px;
	margin-right: 20px;
	color: rgb(34, 34, 34);
	font-weight: 600;
}
.page-layout {
	overflow: auto;
}
.page-left {
	padding-top: 0;
	.model-header {
		padding: 0;
	}
	.form-container {
		padding-top: 20px;
		flex: 1;
		overflow: auto;
		.form-item {
			margin-bottom: 25px;
		}
		.label {
			font-family: Source Han Sans CN;
			font-size: 14px;
			font-weight: 350;
			margin-bottom: 10px;
			color: #9092a0;
		}
		.value {
			font-family: Source Han Sans CN;
			font-size: 13px;
			font-weight: 400;
			color: #3f435e;
		}
	}
}
.page-right {
	::v-deep {
		.el-radio-group {
			.el-radio {
				margin-right: 10px;
			}
		}
		.el-form-item--small.el-form-item {
			margin-bottom: 5px;
		}
	}
	.table-container {
		flex: 1;
		overflow: auto;
	}
	.table {
		margin-bottom: 16px;
		.gc-table {
			height: 180px;
		}
	}
	h5 {
		position: relative;
		margin-bottom: 16px;
		padding-left: 10px;
		font-family: Source Han Sans CN;
		font-size: 14px;
		font-weight: 500;
		color: #000000;
	}
	h5:before {
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		content: '';
		display: block;
		clear: both;
		width: 2px;
		background-color: #2f87fe;
	}
}
.button-group {
	width: 100%;
	height: 40px;
	.btn-create,
	.btn-preview {
		margin-top: 20px;
		border-radius: 4px;
		height: 32px;
	}
	.btn-create {
		width: 216px;
	}
	.btn-preview {
		width: 110px;
		border: 1px solid #2f87fe;
		color: #2f87fe;
	}
}
</style>
