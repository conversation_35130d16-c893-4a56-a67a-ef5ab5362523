<template>
	<div class="container">
		<div class="base-info">
			<GcModelHeader title="抄表信息" :icon="require('@/assets/images/icon/title-file.png')" />
			<GcGroupDetail :data="recordData"></GcGroupDetail>
		</div>
		<div class="border-box"></div>
		<GcModelHeader title="水量修改记录" :icon="require('@/assets/images/icon/title-file.png')" />

		<GcTable
			:loading="loading"
			:columns="columns"
			:table-data="tableData"
			showPage
			:page-size="pageData.size"
			:total="pageData.total"
			:current-page="pageData.current"
			@current-page-change="handleCurrentChange"
		/>
	</div>
</template>

<script>
import { getMeterRecordModfiyLog } from '@/api/meterReading.api'
export default {
	name: '',
	components: {},
	data() {
		return {
			loading: false,
			detailData: {},
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			tableData: [],
			columns: [
				{
					key: 'createTime',
					name: '修改时间',
					tooltip: true,
				},
				{
					key: 'beforeLastMeterReading',
					name: '修改前上次指针',
					tooltip: true,
				},
				{
					key: 'beforeCurMeterReading',
					name: '修改前本次指针',
					tooltip: true,
				},
				{
					key: 'beforeUseAmount',
					name: '修改前本次水量',
					tooltip: true,
				},
				{
					key: 'beforeCheckStatusDesc',
					name: '修改前抄表情况',
					tooltip: true,
				},
				{
					key: 'afterLastMeterReading',
					name: '修改后上次指针',
					tooltip: true,
				},
				{
					key: 'afterCurMeterReading',
					name: '修改后本次指针',
					tooltip: true,
				},
				{
					key: 'afterUseAmount',
					name: '修改后本次水量',
					tooltip: true,
				},
				{
					key: 'afterCheckStatusDesc',
					name: '修改后抄表情况',
					tooltip: true,
				},
				{
					key: 'staffName',
					name: '修改人',
					tooltip: true,
				},
			],
		}
	},
	computed: {
		// 记录信息
		recordData() {
			const list = [
				{
					key: '表册编号',
					value: '--',
					field: 'bookNo',
				},
				{
					key: '表卡编号',
					value: '--',
					field: 'archivesIdentity',
				},
				{
					key: '地址',
					value: '--',
					field: 'addressFullName',
					tooltip: true,
				},
				{
					key: '抄表日期',
					value: '--',
					field: 'thisRecordDate',
				},
				{
					key: '抄表员',
					value: '--',
					field: 'meterReadingStaffName',
				},
				{
					key: '上次指针',
					value: '--',
					field: 'lastMeterReading',
				},
				{
					key: '本次指针',
					value: '--',
					field: 'curMeterReading',
				},
				{
					key: '本次水量',
					value: '--',
					field: 'useAmount',
				},
				{
					key: '抄表情况',
					value: '--',
					field: 'checkStatusDesc',
				},
				{
					key: '抄表状态',
					value: '--',
					field: 'readingStatusDesc',
				},
			]
			return {
				list,
				row: 6,
			}
		},
	},

	// mounted() {
	// 	this.detailData = this.$route.params.rowData
	// 	console.log('mounted this.detailData', this.detailData)
	// },
	activated() {
		this.detailData = this.$route.params.rowData
		console.log('activated this.detailData', this.detailData)
		const newData = Object.assign({}, ...Object.values(this.detailData))
		if (newData.arrearsTotalAmount) {
			this.show = false
		} else {
			this.show = true
		}
		this.pageParams.meterReadingRecordId = this.detailData.meterReadingRecordId
		this._apiGetMeterRecordModfiyLog()
	},
	methods: {
		handleCurrentChange({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this._apiGetMeterRecordModfiyLog()
		},
		_apiGetMeterRecordModfiyLog() {
			const meterReadingRecordId = this.detailData.meterReadingRecordId
			if (!meterReadingRecordId) {
				return
			}
			this.loading = true
			getMeterRecordModfiyLog(Object.assign({ meterReadingRecordId }, this.pageParams))
				.then(res => {
					console.log(res)
					this.loading = false
					this.tableData = res.data?.records
					this.total = res.data?.total
				})
				.catch(err => {
					console.error(err)
					this.loading = false
					this.tableData = []
					this.pageData = {
						current: 1,
						size: 10,
						total: 0,
					}
				})
		},
	},
}
</script>

<style lang="scss" scoped>
.container {
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: #fff;
}
.base-info {
	margin-bottom: 12px;
	height: 185px;
}
.border-box {
	height: 12px;
	background-color: #eceff8;
}
.operate {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;

	.steps-container {
		margin-bottom: 35px;
		width: 1000px;
	}
}
.empty {
	justify-content: center;
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	img {
		margin-bottom: 20px;
	}
	span {
		font-family: Source Han Sans CN;
		font-size: 16px;
		font-weight: 350;
	}
}
.button-group {
	width: 100%;
	display: flex;
	justify-content: center;
	padding-top: 10px;
	padding-bottom: 10px;
	border-top: 1px solid #eef0f3;
}
</style>
