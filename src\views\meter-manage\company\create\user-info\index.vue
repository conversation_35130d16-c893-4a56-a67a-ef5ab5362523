<template>
	<div class="container-wrapper">
		<GcModelHeader
			class="info-title"
			title="用户信息"
			:icon="require('@/assets/images/icon/title-common-parameters.png')"
		/>
		<div class="container">
			<GcFormRow ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<template v-slot:enterpriseNumber>
					<el-input
						v-model="formData.enterpriseNumber"
						placeholder="请选择"
						disabled
						class="enterprise-number"
					>
						<template #append>
							<img src="@/assets/images/icon/search.svg" @click="_showSelectEnterpriseDialog" />
						</template>
					</el-input>
				</template>

				<template v-slot:otherInfo>
					<h5 class="gap-title">开票信息</h5>
				</template>
				<template v-slot:businessLicenseUrl>
					<GcUploadFile v-model="formData.businessLicenseUrl" />
				</template>
				<template v-slot:purchaseContractUrl>
					<GcUploadFile v-model="formData.purchaseContractUrl" />
				</template>
				<template v-slot:otherMobile>
					<el-form-item class="other-mobile" label="其他手机" prop="otherMobile">
						<AddOtherMobile v-model="formData.otherMobile" :mobileList.sync="formData.mobileList" />
					</el-form-item>
				</template>
				<template v-slot:taxpayerIdentity>
					<el-autocomplete
						style="width: 100%"
						v-model="formData.taxpayerIdentity"
						:fetch-suggestions="queryTaxpayerIdentity"
						placeholder="请输入"
						@select="handleTaxpayerIdentitySelect"
						@change="handleTaxpayerIdentityChange"
					>
						<template slot-scope="{ item }">
							<div class="billing-information-item">
								<p class="billing-information-item-row">纳税人识别号：{{ item.taxpayerIdentity }}</p>
								<p class="billing-information-item-row">开票抬头：{{ item.userName }}</p>
							</div>
						</template>
					</el-autocomplete>
				</template>
			</GcFormRow>
			<div class="button-group">
				<button class="gc-button gc-button-three" type="button" @click="handleReset">重 置</button>
				<button class="gc-button gc-button-two" type="button" @click="changeTab('addressChoose')">
					上一项
				</button>
				<button class="gc-button gc-button-two" type="button" @click="changeTab('meterInfo')">下一项</button>
			</div>
		</div>
		<SelectEnterpriseDialog
			ref="selectEnterpriseDialogRef"
			:show.sync="showEnterprise"
			:orgCode="orgCode"
			@select="selectEnterprise"
		></SelectEnterpriseDialog>
	</div>
</template>
<script>
import _ from 'lodash'
import getFormItems from './userForm.js'
import {
	ruleRequired,
	RULE_INTEGERONLY,
	RULE_PHONE,
	RULE_INCORRECTEMAIL,
	ruleMaxLength,
	RULE_INT_ENGLISH,
	RULE_POSTALCODE,
} from '@/utils/rules'
import AddOtherMobile from '../../../components/AddOtherMobile'
import { apiQueryInvoiceBuyer } from '@/api/userManage.api'
import SelectEnterpriseDialog from '../../../components/SelectEnterpriseDialog'

export default {
	name: '',
	props: {
		activeTab: {
			type: String,
			default: '',
		},
		detailData: {
			type: Object,
			default: () => {},
		},
	},
	components: { AddOtherMobile, SelectEnterpriseDialog },
	data() {
		return {
			formData: {
				enterpriseNumber: '',
				collectionAccountId: '', // 企业：页面不展示，但是后端需传
				chargingMethod: '', //  企业： 页面不展示，但是后端需传
				enterpriseName: '',
				userName: '',
				contactPeople: '',
				userMobile: '',
				contactPhone: '',
				zipCode: '',
				contractNum: '',
				email: '',
				mailingAddress: '',
				userSubType: '',
				businessLicenseUrl: [],
				purchaseContractUrl: [],
				otherMobile: '', // 其他手机号
				mobileList: [], // 其他手机号
				invoiceType: '',
				taxpayerIdentity: '',
				openBank: '',
				bankAccount: '',
				buyerName: '',
			},
			// formItems: [],
			formAttrs: {
				labelWidth: '115px',
				labelPosition: 'right',
				rules: {
					userName: [ruleRequired('必填'), ruleMaxLength(32)],
					userMobile: [RULE_PHONE],
					enterpriseNumber: [
						ruleRequired('必填'),
						{
							pattern: /^\d{7}$/,
							message: '必须为数字且7位',
							trigger: '',
						},
					],
					contactPeople: [ruleMaxLength(64)],
					contactPhone: [ruleMaxLength(32)],
					zipCode: [RULE_POSTALCODE],
					contractNum: [ruleMaxLength(64)],
					email: [RULE_INCORRECTEMAIL],
					mailingAddress: [ruleMaxLength(64)],
					otherMobile: [RULE_PHONE],
					openBank: [ruleMaxLength(32)],
					taxpayerIdentity: [RULE_INT_ENGLISH, ruleMaxLength(20)],
					bankAccount: [RULE_INTEGERONLY, ruleMaxLength(32)],
					buyerName: [ruleMaxLength(32)],
				},
			},
			orgCode: '',
			billingInfoDisabled: false,
			// 显示企业选择弹窗
			showEnterprise: false,
		}
	},
	computed: {
		formItems() {
			return getFormItems(this)
		},
	},
	watch: {
		formData: {
			handler: 'validateForm',
			deep: true,
		},
		detailData: {
			handler(v) {
				if (v) {
					this.assignForm(v)
				}
			},
			deep: true,
		},
	},
	created() {
		this.queryTaxpayerIdentity = _.debounce(this.queryTaxpayerIdentity, 250)
	},
	methods: {
		_showSelectEnterpriseDialog() {
			this.showEnterprise = true
		},
		selectEnterprise(obj) {
			this.formData.enterpriseNumber = obj.enterpriseNumber
			this.formData.enterpriseName = obj.enterpriseName
			this.formData.collectionAccountId = obj.collectionAccountId
			this.formData.chargingMethod = obj.chargingMethod
		},
		assignForm(obj) {
			this.formData = Object.assign(this.formData, obj)
			// 其他手机号回显
			this.formData.mobileList = obj.otherContactPhone ? obj.otherContactPhone.split(',') : []
			// 营业执照、购房合同回显
			this.formData.businessLicenseUrl = obj.businessLicenseUrl ? JSON.parse(obj.businessLicenseUrl) : []
			this.formData.purchaseContractUrl = obj.purchaseContractUrl ? JSON.parse(obj.purchaseContractUrl) : []
		},
		async validateForm() {
			const valid = await this.$refs.formRef.validate()
			this.$emit('getValid', 'userInfo', valid)
		},
		handleReset() {
			this.$refs.formRef.resetForm()
			this.validateForm()
		},
		changeTab(v) {
			this.$emit('changeTab', v)
		},
		async queryTaxpayerIdentity(taxpayerIdentity, callback) {
			taxpayerIdentity = taxpayerIdentity || ''
			try {
				let result = await apiQueryInvoiceBuyer({
					taxpayerIdentity,
				})
				result = result || []
				const list = result.map(item => {
					const { taxpayerIdentity } = item
					return { ...item, value: taxpayerIdentity }
				})

				callback(list)
			} catch (e) {
				console.log(e)
				callback([])
			}
		},
		handleTaxpayerIdentitySelect(item) {
			const { userName, openBank, bankAccount } = item
			this.formData.openBank = openBank
			this.formData.buyerName = userName
			this.formData.bankAccount = bankAccount
			this.billingInfoDisabled = true
		},
		handleTaxpayerIdentityChange() {
			this.billingInfoDisabled = false
		},
	},
	mounted() {
		this.validateForm()
	},
}
</script>

<style lang="scss" scoped>
.container-wrapper {
	height: 100%;
}
.container {
	padding-right: 20px;
	height: calc(100% - 60px);
	overflow-y: auto;
	overflow-x: hidden;
}
.gap-title {
	padding: 0 20px;
	color: #222222;
	font-size: 14px;
	font-weight: bold;
}
.button-group {
	margin-top: 20px;
	padding: 0 20px;
	.gc-button {
		margin-right: 8px;
	}
}
.other-mobile {
	::v-deep {
		.el-form-item__error {
			position: absolute;
			top: 36px;
		}
	}
}
.enterprise-number {
	::v-deep {
		.el-input__inner {
			border: 1px solid #e4e7ed !important;
		}
	}
}
.billing-information-item {
	padding: 8px 0;
}
</style>
