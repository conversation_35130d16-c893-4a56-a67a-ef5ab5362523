<template>
	<CardContainer :cardDetail="cardDetail">
		<div class="content-box">
			<div class="top">
				<el-select v-model="selValue" placeholder="请选择" @change="getList">
					<el-option
						v-for="item in natureOptions"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					></el-option>
				</el-select>
				<el-radio-group v-model="tabValue" @input="getList">
					<el-radio-button :label="0">月</el-radio-button>
					<el-radio-button :label="1">年</el-radio-button>
				</el-radio-group>
			</div>
			<div class="bottom">
				<GcBar1
					:unit="chartOptions.unit"
					:xData="chartOptions.xData"
					:seriesData="chartOptions.seriesData"
					:seriesName="chartOptions.seriesName"
					barColor="rgba(172,215,186,0.6)"
					stackBarColor="rgba(172,215,186)"
					:startDataZoom="50"
					:endDataZoom="60"
				/>
			</div>
		</div>
	</CardContainer>
</template>

<script>
import CardContainer from './CardContainer.vue'
import { apiGetWaterCostChart } from '@/api/home.api'
export default {
	name: 'SalesInput',
	components: { CardContainer },
	props: {
		natureOptions: {
			type: Array,
			default: () => [],
		},
		orgCode: {
			type: String,
			default: '',
		},
	},
	watch: {
		natureOptions: {
			handler: function (arr) {
				if (arr && arr.length) {
					this.selValue = arr[0].value
				}
			},
			deep: true,
		},
		orgCode() {
			this.getList()
		},
		'cardDetail.activeTab'(v) {
			const typeMap = this.cardDetail.tabList.reduce((map, item) => {
				map[item.value] = item.name.replace('表卡', '')
				return map
			}, {})
			this.chartOptions.seriesName = typeMap[v] || ''
			this.getList()
		},
	},
	data() {
		return {
			cardDetail: {
				titleList: [
					{
						label: '水费',
						value: '--',
						unit: '￥',
					},
					{
						label: '同期比',
						value: '--',
						unit: '%',
					},
					{
						label: '全年累计',
						value: '--',
						unit: '￥',
					},
					{
						label: '同期比',
						value: '--',
						unit: '%',
					},
				],
				activeTab: '3',
				tabList: [
					{
						name: '居民表卡',
						value: '3',
					},
					{
						name: '企业表卡',
						value: '4',
					},
				],
				bg: require('@/assets/images/bg/home-bg2.png'),
				name: '销售收入',
			},
			tabValue: 0,
			selValue: '',
			chartOptions: {
				unit: '单位: 万元',
				xData: [],
				seriesData: [],
				seriesName: '居民',
			},
		}
	},
	computed: {},
	created() {},
	methods: {
		async getList() {
			try {
				const {
					annualTotal,
					currentMonthAmount,
					monthSameRatio,
					yearSameRatio,
					natureAmtList = [],
				} = await apiGetWaterCostChart({
					dimension: this.tabValue,
					orgCode: this.orgCode,
					userType: this.cardDetail.activeTab,
				})
				this.cardDetail.titleList[0].value = currentMonthAmount
				this.cardDetail.titleList[1].value = monthSameRatio
				this.cardDetail.titleList[2].value = annualTotal
				this.cardDetail.titleList[3].value = yearSameRatio
				if (Array.isArray(natureAmtList) && natureAmtList.length) {
					const result = natureAmtList.find(item => item.natureNo === this.selValue)
					if (result && Array.isArray(result.children) && result.children.length) {
						this.chartOptions.xData = result.children.map(item => item.natureName)
						this.chartOptions.seriesData = result.children.map(item => item.useAmount)
						this.chartOptions.xData.unshift(result.natureName)
						this.chartOptions.seriesData.unshift(result.useAmount)
					}
				}
			} catch (error) {
				console.error(error)
			}
		},
	},
}
</script>
