import service from './request'
import { CPM, READING, BILLING } from '@/consts/moduleNames'

// 获取坊别数据
export function getAlleyMap(params) {
	return service({
		url: `${CPM}/alley/getAlleyMap`,
		method: 'get',
		params,
	})
}

// 获取抄表员工数据
export function getStaffMap(params) {
	return service({
		url: `${CPM}/meterReadingStaff/getStaffMap`,
		method: 'get',
		params,
	})
}
// 根据工种信息查询员工下拉框
export function queryStaffByType(data) {
	return service({
		url: `${CPM}/meterReadingStaff/queryStaffByType`,
		method: 'post',
		data,
	})
}

// 生成抄表编号
export function generateBookNo(data) {
	return service({
		url: `${READING}/meterReadingBook/getBookNo`,
		method: 'post',
		data,
	})
}

// 获取册本分页列表
export function getBookList(data) {
	return service({
		url: `${READING}/meterReadingBook/getBookList`,
		method: 'post',
		data,
	})
}
// 获取册本分页列表(无权限)
export function getBookListNoAuth(data) {
	return service({
		url: `${READING}/meterReadingBook/getBookListNoAuthAnnotation`,
		method: 'post',
		data,
	})
}
// 获取册本下拉框数据
export function getBookMap(params) {
	return service({
		url: `${READING}/meterReadingBook/getBookMap`,
		method: 'get',
		params,
	})
}
// 根据id获取册本详情
export function getBookDetailById(params) {
	return service({
		url: `${READING}/meterReadingBook/getBookDetail`,
		method: 'get',
		params,
	})
}
// 册本新建
export function addMeterReadingBook(data) {
	return service({
		url: `${READING}/meterReadingBook/addBook`,
		method: 'post',
		data,
	})
}
// 编辑表册编号
export function bookNoEditSubmit(data) {
	return service({
		url: `${READING}/meterReadingBook/updateBookNo`,
		method: 'post',
		data,
	})
}
// 册本编辑
export function updateMeterReadingBook(data) {
	return service({
		url: `${READING}/meterReadingBook/updateBook`,
		method: 'post',
		data,
	})
}
export function updateMeterReadingBook2(data) {
	return service({
		url: `${READING}/meterReadingBook/updateBook2`,
		method: 'post',
		data,
	})
}
// 删除册本
export function deleteMeterReadingBook(data) {
	return service({
		url: `${READING}/meterReadingBook/deleteBook`,
		method: 'post',
		data,
	})
}
// 册本表卡明细-导出
export function bookArchivesListExport(data) {
	return service({
		url: `${READING}/report/bookArchivesList/export/excel`,
		method: 'post',
		data,
		responseType: 'blob',
	})
}
// 册本列表导出
export function bookListExport(data) {
	return service({
		url: `${READING}/report/bookList/export/excel`,
		method: 'post',
		data,
		responseType: 'blob',
	})
}
// 批量更改测本抄表员
export function batchChangeMeterReadingStaff(data) {
	return service({
		url: `${READING}/meterReadingBook/meterReadingStaff`,
		method: 'PUT',
		data,
	})
}
// 册本管理 - 生成抄表任务
export function createFirstTask(params) {
	return service({
		url: `${READING}/meterReadingTask/createFirstTask`,
		method: 'get',
		params,
	})
}
// 册本视图 - 抄表任务 - 生成抄表任务
export function createFirstTask2(params) {
	return service({
		url: `${READING}/meterReadingTask/createFirstTask2`,
		method: 'get',
		params,
	})
}
// 获取册本表卡记录列表
export function getBookRecordList(data) {
	return service({
		url: `${READING}/meterReadingBook/getBookArchivesList`,
		method: 'post',
		data,
	})
}
export function getBookRecordList2(data) {
	return service({
		url: `${READING}/meterReadingBook/getBookArchivesList2`,
		method: 'post',
		data,
	})
}
// 修改册内序号
export function updateArchivesSeq(data) {
	return service({
		url: `${READING}/meterReadingBook/updateArchivesSeq`,
		method: 'post',
		data,
	})
}
// 修改册内序号1
export function updateArchivesSeq1(data) {
	return service({
		url: `${READING}/meterReadingBook/updateArchivesSeq1`,
		method: 'post',
		data,
	})
}
// 修改册内序号2
export function updateArchivesSeq2(data) {
	return service({
		url: `${READING}/meterReadingBook/updateArchivesSeq2`,
		method: 'post',
		data,
	})
}
// 册本视图 - 操作记录
export function getBookModifyRecordList(data) {
	return service({
		url: `${READING}/meterReadingBook/getBookModifyRecordList`,
		method: 'post',
		data,
	})
}
// 册本调整 - 左移右移
export function bookArchivesMove(data) {
	return service({
		url: `${READING}/meterReadingBook/bookArchivesMove`,
		method: 'post',
		data,
	})
}
// 册本调整 - 置换
export function bookArchivesDisplace(data) {
	return service({
		url: `${READING}/meterReadingBook/bookArchivesDisplace`,
		method: 'post',
		data,
	})
}

// 抄表任务管理、册本视图 - 抄表任务列表
export function getTaskList(data) {
	return service({
		url: `${READING}/meterReadingTask/getTaskList`,
		method: 'post',
		data,
	})
}
export function getTaskList2(data) {
	return service({
		url: `${READING}/meterReadingTask/getTaskList2`,
		method: 'post',
		data,
	})
}
// 关闭抄表任务
export function closeTask1(params) {
	return service({
		url: `${READING}/meterReadingTask/closeTask1`,
		method: 'get',
		params,
	})
}
export function closeTask2(params) {
	return service({
		url: `${READING}/meterReadingTask/closeTask2`,
		method: 'get',
		params,
	})
}
// 获取抄表任务详情
export function getTaskDetail(params) {
	return service({
		url: `${READING}/meterReadingTask/getTaskDetail`,
		method: 'get',
		params,
	})
}
// 抄表记录列表
export function getRecordList(data) {
	return service({
		url: `${READING}/meterReadingTask/getRecordList`,
		method: 'post',
		data,
	})
}
export function getRecordList2(data) {
	return service({
		url: `${READING}/meterReadingTask/getRecordList2`,
		method: 'post',
		data,
	})
}
// 抄表录入
export function updateMeterReadingRecord(data) {
	return service({
		url: `${READING}/meterReadingTask/updateMeterReadingRecord`,
		method: 'post',
		data,
	})
}
export function updateMeterReadingRecord2(data) {
	return service({
		url: `${READING}/meterReadingTask/updateMeterReadingRecord2`,
		method: 'post',
		data,
	})
}
export function updateMeterReadingRecord3(data) {
	return service({
		url: `${READING}/meterReadingTask/updateMeterReadingRecord3`,
		method: 'post',
		data,
	})
}
// 对抄表任务送内复
export function autoReviewByTask(data) {
	return service({
		url: `${READING}/meterReadingTask/autoReviewByTask`,
		method: 'post',
		data,
	})
}
export function autoReviewByTask2(data) {
	return service({
		url: `${READING}/meterReadingTask/autoReviewByTask2`,
		method: 'post',
		data,
	})
}
// 抄表录入整册送内复
export function autoReviewByTask3(data) {
	return service({
		url: `${READING}/meterReadingTask/autoReviewByTask3`,
		method: 'post',
		data,
	})
}
// 对抄表记录送内复
export function autoReviewByRecord(data) {
	return service({
		url: `${READING}/meterReadingTask/autoReviewByRecord`,
		method: 'post',
		data,
	})
}

// 获取抄表复核列表
export function getReviewList(data) {
	return service({
		url: `${READING}/meterReadingReview/getReviewList`,
		method: 'post',
		data,
	})
}
// 抄表复核详情列表查询(审核异常、审核通过、水量拆分、复核通过)
export function getReviewDetailList(data) {
	return service({
		url: `${READING}/meterReadingReview/getReviewDetailList`,
		method: 'post',
		data,
	})
}
export function getReviewDetailList2(data) {
	return service({
		url: `${READING}/meterReadingReview/getReviewDetailList2`,
		method: 'post',
		data,
	})
}
export function getReviewDetailList3(data) {
	return service({
		url: `${READING}/meterReadingReview/getReviewDetailList3`,
		method: 'post',
		data,
	})
}
export function getReviewDetailList4(data) {
	return service({
		url: `${READING}/meterReadingReview/getReviewDetailList4`,
		method: 'post',
		data,
	})
}
// 复核通过
export function reviewPass(data) {
	return service({
		url: `${READING}/meterReadingReview/reviewPass`,
		method: 'post',
		data,
	})
}
export function reviewPass1(data) {
	return service({
		url: `${READING}/meterReadingReview/batchReviewPass1`,
		method: 'post',
		data,
	})
}
export function reviewPass2(data) {
	return service({
		url: `${READING}/meterReadingReview/reviewPass2`,
		method: 'post',
		data,
	})
}
export function reviewPass3(data) {
	return service({
		url: `${READING}/meterReadingReview/batchReviewPass2`,
		method: 'post',
		data,
	})
}
// 复核驳回
export function reviewReject(data) {
	return service({
		url: `${READING}/meterReadingReview/reviewReject`,
		method: 'post',
		data,
	})
}
export function reviewReject1(data) {
	return service({
		url: `${READING}/meterReadingReview/batchReviewReject1`,
		method: 'post',
		data,
	})
}
export function reviewReject2(data) {
	return service({
		url: `${READING}/meterReadingReview/reviewReject2`,
		method: 'post',
		data,
	})
}
export function reviewReject3(data) {
	return service({
		url: `${READING}/meterReadingReview/batchReviewReject2`,
		method: 'post',
		data,
	})
}
// 全部生成账单
export function createMeterTaskBill(data) {
	return service({
		url: `${BILLING}/bill/meter-task-create`,
		method: 'post',
		data,
	})
}
// 生成账单
export function createBill(data) {
	return service({
		url: `${BILLING}/bill/create`,
		method: 'post',
		data,
	})
}
// 获取水量拆分列表
export function getWaterSpiltList(data) {
	return service({
		url: `${READING}/meterReadingReview/getWaterSpiltList`,
		method: 'post',
		data,
	})
}
// 水量拆分
export function waterSpilt(data) {
	return service({
		url: `${READING}/meterReadingReview/waterSpilt`,
		method: 'post',
		data,
	})
}
// 水量校验
export function checkWater(data) {
	return service({
		url: `${READING}/meterReadingReview/checkWater`,
		method: 'post',
		data,
	})
}
// 虚表水量拆分-修改本次水量
export function updateWaterSpilt(data) {
	return service({
		url: `${READING}/meterReadingReview/updateWaterSpilt`,
		method: 'post',
		data,
	})
}

// 追收抄表
// 查询档案信息及最后一次抄表记录
export function queryLastMeterReadingRecord(data) {
	return service({
		url: `${READING}/meterReadingTask/queryLastMeterReadingRecord`,
		method: 'post',
		data,
	})
}
// 计算
export function meterReadingCollection(data) {
	return service({
		url: `${READING}/meterReadingTask/meterReadingCollection`,
		method: 'post',
		data,
	})
}
// 修改水量
export function modifyWaterVolume(data) {
	return service({
		url: `${READING}/meterReadingTask/modifyWaterVolume`,
		method: 'post',
		data,
	})
}
// 确定追收并生成账单
export function createMeterReadingCollection(data) {
	return service({
		url: `${READING}/meterReadingTask/createMeterReadingCollection`,
		method: 'post',
		data,
		timeout: 120000,
	})
}

// 册本移交
// 表册编号选择下拉数据
export function meterCardOptionsData(params) {
	return service({
		url: `${READING}/meterReadingBook/transfer/bookChoose`,
		method: 'get',
		params,
	})
}
// 册本表卡选择列表
export function meterCardOpsPage(data) {
	return service({
		url: `${READING}/meterReadingBook/transfer/meterCardOpsPage`,
		method: 'post',
		data,
	})
}
// 册本表卡选择列表
export function meterCardChoose(data) {
	return service({
		url: `${READING}/meterReadingBook/transfer/meterCardChoose`,
		method: 'post',
		data,
	})
}

// 移交清单列表查询
export function meterCardPreviewPage(data) {
	return service({
		url: `${READING}/meterReadingBook/transfer/preview`,
		method: 'post',
		data,
	})
}
// 册本移交记录查看 弹窗使用
export function meterCardRecordPreviewPage(data) {
	return service({
		url: `${READING}/meterReadingBook/record/preview`,
		method: 'post',
		data,
	})
}

// 册本移交登记
export function meterCardRegister(data) {
	return service({
		url: `${READING}/meterReadingBook/transfer/register`,
		method: 'post',
		data,
	})
}
// 册本移交记录
export function queryTransferRecordPage(data) {
	return service({
		url: `${READING}/meterReadingBook/transfer/queryTransferPage`,
		method: 'post',
		data,
	})
}
// 导出移交清单
export function meterCardTransferExport(data) {
	return service({
		url: `${READING}/meterReadingBook/transfer/export`,
		method: 'post',
		data,
		responseType: 'blob',
	})
}

// 导入抄表数据Excel模板下载
export function downloadMeterReadingRecordTemplate(data) {
	return service({
		url: `${READING}/meterReadingTask/getDownloadTemplate`,
		method: 'post',
		data,
		responseType: 'blob',
	})
}

// 抄表数据导入
export function importMeterReadingRecordExcel(data) {
	return service({
		url: `${READING}/meterReadingTask/importMeterReadingRecordExcel`,
		method: 'post',
		data,
		headers: {
			'Content-Type': 'multipart/form-data',
		},
		responseType: 'blob',
	})
}

// 新的抄表复核详情列表查询(审核异常、审核通过、水量拆分、复核通过)
export function getReviewDetailListNewV2(data) {
	return service({
		url: `${READING}/meterReadingReview/v2/getReviewDetailList`,
		method: 'post',
		data,
	})
}

// 新的抄表复核详情 - 查询册本
export function getReviewNewBooks(data) {
	return service({
		url: `${READING}/meterReadingReview/getBooks`,
		method: 'post',
		data,
	})
}

// 新的抄表复核详情 - 批量审核通过并生成账单
export function asyncBatchReviewPassAndCreateBill(data) {
	return service({
		url: `${READING}/meterReadingReview/asyncBatchReviewPassAndCreateBill`,
		method: 'post',
		data,
	})
}

// 新的抄表复核详情 - 获取批量通过结果
export function getBatchReviewResult(params) {
	return service({
		url: `${READING}/meterReadingReview/getBatchReviewResult`,
		method: 'get',
		params,
	})
}

// 新的抄表复核详情 - 驳回
export function batchReviewRejectV2(data) {
	return service({
		url: `${READING}/meterReadingReview/v2/batchReviewReject`,
		method: 'post',
		data,
	})
}

// 新的抄表复核详情 - 水量校验
export function checkWaterV2(data) {
	return service({
		url: `${READING}/meterReadingReview/v2/batchCheckWater`,
		method: 'post',
		data,
	})
}
