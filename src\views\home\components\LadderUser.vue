<template>
	<div class="ladder-user">
		<div class="title-wrapper">阶梯用户提示</div>
		<div class="table-container">
			<GcTable
				:loading="loading"
				:columns="columns"
				:table-data="tableData"
				showPage
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				@current-page-change="changePage"
			/>
		</div>
	</div>
</template>

<script>
import { apiGetObtainUserLadderLevelChart } from '@/api/home.api'
export default {
	name: 'LadderUser',
	props: {
		orgCode: {
			type: String,
			default: '',
		},
	},
	watch: {
		orgCode(v) {
			if (v) {
				if (!this.$has('cpm_home_charts_obtainUserLadderLevelChart')) {
					return
				}
				this.getList()
			}
		},
	},
	data() {
		return {
			columns: [
				{
					key: 'archivesIdentity',
					name: '表卡编号',
					tooltip: true,
				},
				{
					key: 'userName',
					name: '用户名称',
					tooltip: true,
				},
				{
					key: 'addressFullName',
					name: '地址',
					tooltip: true,
				},
				{
					key: 'yearQuantity',
					name: '年累计水量(m³)',
					tooltip: true,
				},
				{
					key: 'monthCurrent',
					name: '进入阶梯水量月份',
					tooltip: true,
				},
				{
					key: 'sf',
					name: '当月水费(￥)',
					tooltip: true,
				},
				{
					key: 'wsf',
					name: '污水处理费(￥)',
					tooltip: true,
				},
			],
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			loading: false,
		}
	},

	methods: {
		async getList() {
			try {
				this.tableData = []
				const { records, total } = await apiGetObtainUserLadderLevelChart({
					orgCode: this.orgCode,
					current: this.pageData.current,
					size: this.pageData.size,
				})

				if (Array.isArray(records) && records.length) {
					this.tableData = records
				}
				this.pageData.total = total
			} catch (error) {
				console.error(error)
			}
		},
		changePage({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
	},
}
</script>

<style lang="scss" scoped>
.ladder-user {
	display: flex;
	flex-direction: column;
	border: 1px solid #d5d8e2;
	border-radius: 8px;
	padding: 10px;
	width: 100%;
	height: 468px;
	.title-wrapper {
		display: flex;
		justify-content: space-between;
		margin-bottom: 10px;
		border-radius: 8px;
		padding: 12px;
		background: linear-gradient(92.14deg, rgba(151, 203, 255, 0.4) -1.61%, rgba(191, 219, 255, 0.35) 96.3%);
		font-family: Alimama ShuHeiTi;
		font-weight: 700;
		font-size: 18px;
		color: #000000;
	}
}
.table-container {
	flex: 1;
	height: 0;
}
</style>
