import { getLodop } from '../lodop/LodopFuncs'

// 欠费通知单
export const generateArrearageTemplate = (data, type = 'preview') => {
	const LODOP = getLodop()

	LODOP.PRINT_INIT('过期水费催缴停水通知单')
	LODOP.PRINT_INITA(0, 0, 800, 1122, '过期水费催缴停水通知单')
	LODOP.SET_PRINT_MODE('PROGRAM_CONTENT_BYVAR', true)
	LODOP.SET_PRINT_STYLE('FontSize', 12)
	LODOP.SET_PRINT_STYLE('FontColor', '#808080')

	LODOP.ADD_PRINT_TEXT(143, 380, 215, 20, '缴费账号')
	LODOP.ADD_PRINT_TEXT(185, 188, 512, 20, '用户名称')
	LODOP.ADD_PRINT_TEXT(226, 192, 141, 20, 'xxxxxx区')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(226, 366, 154, 20, 'xxxx路')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(226, 608, 80, 20, 'xxxx号')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(268, 185, 51, 20, '2024')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(268, 255, 28, 20, '12')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(268, 301, 31, 20, '31')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(309, 190, 511, 20, '停水原因')
	LODOP.ADD_PRINT_TEXT(351, 190, 267, 20, '经办人')
	LODOP.ADD_PRINT_TEXT(516, 148, 189, 20, '用户名称')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(559, 226, 132, 20, 'xxxx区')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(559, 386, 135, 20, 'xxxx路')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(559, 607, 65, 20, 'xxxx号')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(601, 196, 95, 20, '缴费账号')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(602, 304, 54, 20, '2024')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(602, 380, 40, 20, '12')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)

	LODOP.ADD_PRINT_TEXT(642, 496, 153, 20, '甘井子（东）')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)

	LODOP.ADD_PRINT_TEXT(684, 222, 132, 20, '1555.2')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(726, 117, 45, 20, '2024')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(726, 184, 30, 20, '12')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(726, 232, 30, 20, '31')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)

	LODOP.ADD_PRINT_TEXT(809, 189, 145, 20, '0571-8856855')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)

	LODOP.ADD_PRINT_TEXT(934, 498, 113, 20, '甘井子（东）')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)

	LODOP.ADD_PRINT_TEXT(976, 563, 121, 20, '经办人')

	LODOP.ADD_PRINT_TEXT(1018, 480, 46, 20, '2024')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(1018, 544, 30, 20, '12')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)
	LODOP.ADD_PRINT_TEXT(1018, 592, 33, 20, '31')
	LODOP.SET_PRINT_STYLEA(0, 'Alignment', 2)

	LODOP.ADD_PRINT_SETUP_BKIMG(`<img border='0' src='print-template/arrearage.jpg'>`)
	LODOP.SET_SHOW_MODE('BKIMG_WIDTH', 800)
	LODOP.SET_SHOW_MODE('BKIMG_HEIGHT', 1122)

	// 预览、打印时包含背景图片
	LODOP.SET_SHOW_MODE('BKIMG_IN_PREVIEW', 1)
	LODOP.SET_SHOW_MODE('BKIMG_PRINT', true)

	if (type === 'preview') {
		LODOP.PREVIEW()
		// LODOP.PRINT_DESIGN();
	} else {
		LODOP.PRINT()
	}
}

export const arrearagePrint = async () => {
	try {
		//   await xxx
		generateArrearageTemplate({})
	} catch (error) {
		console.error(error)
	}
}
