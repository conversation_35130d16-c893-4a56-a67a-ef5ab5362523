import { staffStatusOptions } from '@/consts/optionList.js'
import { getfilterName } from '@/utils'
export function getColumn(_this) {
	return [
		{
			key: 'orgName',
			name: '营业分公司',
			tooltip: true,
		},
		{
			key: 'departmentName',
			name: '所属部门',
			tooltip: true,
		},
		{
			key: 'staffName',
			name: '员工名称',
			tooltip: true,
		},
		{
			key: 'staffCode',
			name: '员工编号',
			tooltip: true,
		},
		{
			key: 'modifyTime',
			name: '修改时间',
			tooltip: true,
		},
		{
			key: 'staffAttribute',
			name: '属性',
			render: (h, row) => {
				const valueStr = row.staffAttribute == 1 ? '入户' : row.staffAttribute == 2 ? '远传' : '--'
				return h('span', {}, valueStr)
			},
		},
		{
			key: 'staffPhone',
			name: '电话',
			tooltip: true,
		},
		{
			key: 'status',
			name: '状态',
			tooltip: true,
			render: (h, row) => {
				return h('span', {}, getfilterName(staffStatusOptions, row.status))
			},
		},
		{
			key: 'operate',
			name: '操作',
			fixed: 'right',
			hide: !_this.$has(['cpm_meterReadingStaff_updateWorkStaff']),
		},
	]
}
