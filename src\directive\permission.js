import store from '@/store'

function checkPermission(el, binding) {
	const { value } = binding

	const permissions = store.getters && store.getters.permissions
	if (!value) {
		return
		// throw new Error(
		//   `need permission! Like v-has="['archiveAdd','monitorList']" or v-has="archiveAdd"`
		// );
	} else {
		const valType = Object.prototype.toString.call(value)
		if (valType === '[object Array]' && value.length > 0) {
			const hasPermission = permissions.some(role => {
				return value.includes(role)
			})
			if (!hasPermission) {
				el.parentNode && el.parentNode.removeChild(el)
			}
		} else if (valType === '[object String]') {
			if (!permissions.includes(value)) {
				el.parentNode && el.parentNode.removeChild(el)
			}
		}
	}
}

export default {
	inserted(el, binding) {
		checkPermission(el, binding)
	},
	update(el, binding) {
		checkPermission(el, binding)
	},
}
