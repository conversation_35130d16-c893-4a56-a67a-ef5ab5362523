export default function (data, props) {
	const seriesData = props.seriesData || []
	const showDataZoom = props.showDataZoom
	const axisLabelRotate = props.axisLabelRotate
	const startDataZoom = props.startDataZoom || 10
	const endDataZoom = props.endDataZoom || 90
	return {
		tooltip: {
			trigger: 'axis',
		},
		grid: {
			left: 60,
			right: 20,
			top: 40,
			bottom: 40,
			containLabel: true,
			...props.grid,
		},
		dataZoom: [
			{
				show: showDataZoom,
				height: 12,
				xAxisIndex: [0],
				bottom: '8%',
				start: showDataZoom ? startDataZoom : 0,
				end: showDataZoom ? endDataZoom : 100,
				handleIcon:
					'path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z',
				handleSize: '110%',
				handleStyle: {
					color: '#d3dee5',
				},
				textStyle: {
					color: '#fff',
				},
				borderColor: '#90979c',
			},
			{
				type: 'inside',
				show: true,
				height: 15,
				start: 1,
				end: 35,
			},
		],
		legend: {
			selectedMode: false,
			...props.legend,
		},
		xAxis: {
			type: 'category',
			data: [],
			axisLabel: {
				interval: 0,
				rotate: axisLabelRotate ? axisLabelRotate : 0,
			},
			...props.xAxis,
		},
		yAxis: {
			type: 'value',
			nameTextStyle: {
				padding: [0, 0, 0, -30], //间距分别是 上 右 下 左
			},
			...props.yAxis,
		},
		series: seriesData,
	}
}
