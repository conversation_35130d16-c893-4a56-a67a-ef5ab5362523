export default function (data, props) {
	// const maxValue = Math.max(...data)
	// const stackData = data.map(() => {
	// 	return maxValue * 0.05
	// })
	const barColor = props.barColor || 'rgba(137, 121, 255, 0.7)'
	// const stackBarColor = props.stackBarColor || '#8979FF'
	const seriesName = props.seriesName || ''
	const showDataZoom = props.showDataZoom
	const axisLabelRotate = props.axisLabelRotate
	const startDataZoom = props.startDataZoom || 10
	const endDataZoom = props.endDataZoom || 90

	return {
		grid: {
			left: 60,
			right: 20,
			top: 40,
			bottom: 40,
			containLabel: true,
		},
		tooltip: {
			trigger: 'axis',
		},
		dataZoom: [
			{
				show: showDataZoom,
				height: 12,
				xAxisIndex: [0],
				bottom: '8%',
				start: showDataZoom ? startDataZoom : 0,
				end: showDataZoom ? endDataZoom : 100,
				handleIcon:
					'path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z',
				handleSize: '110%',
				handleStyle: {
					color: '#d3dee5',
				},
				textStyle: {
					color: '#fff',
				},
				borderColor: '#90979c',
			},
			{
				type: 'inside',
				show: true,
				height: 15,
				start: 1,
				end: 35,
			},
		],
		legend: {
			selectedMode: false,
			icon: 'rect',
			right: 'center',
			top: 13,
			itemWidth: 20,
			itemHeight: 10,
			textStyle: {
				color: '#555',
			},
			...props.legend,
		},
		xAxis: {
			type: 'category',
			data: [],
			axisLabel: {
				interval: 0,
				rotate: axisLabelRotate ? axisLabelRotate : 0,
			},
			...props.xAxis,
		},
		yAxis: {
			type: 'value',
			nameTextStyle: {
				padding: [0, 0, 0, -30], //间距分别是 上 右 下 左
			},
			...props.yAxis,
		},
		series: [
			{
				name: seriesName,
				stack: '叠加',
				data,
				type: 'bar',
				showBackground: true,
				backgroundStyle: {
					color: 'rgba(180, 180, 180, 0.2)',
				},
				label: {
					show: true,
					position: 'top',
					textStyle: {
						color: '#000',
					},
				},
				itemStyle: {
					color: barColor,
				},
				barWidth: '40%',
				barMaxWidth: 40, // 新增，最大宽度40px
				barMinWidth: 10, // 新增，最小宽度10px
			},
			// {
			// 	name: '',
			// 	type: 'bar',
			// 	stack: '叠加',
			// 	tooltip: { show: false },
			// 	itemStyle: {
			// 		color: stackBarColor,
			// 	},
			// 	data: stackData,
			// },
		],
	}
}
