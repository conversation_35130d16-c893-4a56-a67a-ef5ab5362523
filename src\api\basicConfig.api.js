import service from './request'
import { CPM } from '@/consts/moduleNames'

// 坊别管理分页信息查询
export function queryAlleyPage(data) {
	return service({
		url: `${CPM}/alley/queryAlleyPage`,
		method: 'post',
		data,
	})
}
// 新增坊别
export function addAlley(data) {
	return service({
		url: `${CPM}/alley/addAlley`,
		method: 'post',
		data,
	})
}
// 生成唯一坊别编号
export function obtainCode() {
	return service({
		url: `${CPM}/alley/obtainCode`,
		method: 'get',
	})
}
// 验证坊别编号是否重复
export function checkCodeIsRepeat(params) {
	return service({
		url: `${CPM}/alley/verifyAlleyCode`,
		method: 'get',
		params,
	})
}
// 修改坊别
export function updateAlley(data) {
	return service({
		url: `${CPM}/alley/updateAlley`,
		method: 'post',
		data,
	})
}
// 删除坊别
export function deleteAlley(params) {
	return service({
		url: `${CPM}/alley/deleteAlley`,
		method: 'get',
		params,
	})
}

// 新增结账日
export function addBalanceSheetDate(data) {
	return service({
		url: `${CPM}/balanceSheetDate/addBalanceSheetDate`,
		method: 'post',
		data,
	})
}
// 修改结账日
export function updateBalanceSheetDate(data) {
	return service({
		url: `${CPM}/balanceSheetDate/updateBalanceSheetDate`,
		method: 'post',
		data,
	})
}
// 结账日清单列表查询
export function queryBalanceSheetDateList(data) {
	return service({
		url: `${CPM}/balanceSheetDate/queryBalanceSheetDateList`,
		method: 'post',
		data,
	})
}
// 查询年份列表查询
export function queryBalanceSheetDateYearList(data) {
	return service({
		url: `${CPM}/balanceSheetDate/queryBalanceSheetDateYearList`,
		method: 'post',
		data,
	})
}

// 分公司管理
// 营业分公司列表查询
export function queryBusinessHallList(data) {
	return service({
		url: `${CPM}/businessHall/queryBusinessHallList`,
		method: 'post',
		data,
	})
}
// 营业分公司下拉框
export function queryBusinessHallMap() {
	return service({
		url: `${CPM}/businessHall/queryBusinessHallMap`,
		method: 'get',
	})
}
export function queryBusinessHallAll() {
	return service({
		url: `${CPM}/businessHall/queryBusinessHallAll`,
		method: 'get',
	})
}

// 开户银行下拉框
export function queryCollectionBankList(params) {
	return service({
		url: `${CPM}/businessHall/queryCollectionBankList`,
		method: 'get',
		params,
	})
}
// 支行行号下拉框
export function queryConsignBankCodeList(params) {
	return service({
		url: `${CPM}/businessHall/queryConsignBankCodeList`,
		method: 'get',
		params,
	})
}

// 营业分公司编辑基本信息
export function updateBusinessHallBaseInfo(data) {
	return service({
		url: `${CPM}/businessHall/updateBusinessHallBaseInfo`,
		method: 'post',
		data,
	})
}
// 编码调整
export function updateBusinessHallCode(data) {
	return service({
		url: `${CPM}/businessHall/updateBusinessHallCode`,
		method: 'post',
		data,
	})
}
// 册本位数调整
export function updateBusinessHallDigits(data) {
	return service({
		url: `${CPM}/businessHall/updateBusinessHallDigits`,
		method: 'post',
		data,
	})
}
// 查询开账日详情
export function queryAccountOpeningDate(data) {
	return service({
		url: `${CPM}/businessHall/queryAccountOpeningDate`,
		method: 'post',
		data,
	})
}
// 查询开账日详情
export function updateAccountOpeningDate(data) {
	return service({
		url: `${CPM}/businessHall/updateAccountOpeningDate`,
		method: 'post',
		data,
	})
}

// 用水性质
// 获取用水性质分页列表
export function queryWaterNatureList(data) {
	return service({
		url: `${CPM}/price-nature/list`,
		method: 'post',
		data,
	})
}
// 新增用水性质
export function addWaterNature(data) {
	return service({
		url: `${CPM}/price-nature/add`,
		method: 'post',
		data,
	})
}
// 修改用水性质
export function updateWaterNature(data) {
	return service({
		url: `${CPM}/price-nature/modify`,
		method: 'post',
		data,
	})
}
// 获取用水性质树
export function queryWaterNatureTree() {
	return service({
		url: `${CPM}/price-nature/tree`,
		method: 'get',
	})
}

// 账项维护
// 查询账项列表
export function queryBillItemList(data) {
	return service({
		url: `${CPM}/bill-item/query`,
		method: 'post',
		data,
	})
}
// 新增账项
export function addBillItem(data) {
	return service({
		url: `${CPM}/bill-item/add`,
		method: 'post',
		data,
	})
}
// 删除账项
export function deleteBillItem(data) {
	return service({
		url: `${CPM}/bill-item/remove`,
		method: 'post',
		data,
	})
}
// 修改账项
export function updateBillItem(data) {
	return service({
		url: `${CPM}/bill-item/modify`,
		method: 'post',
		data,
	})
}
// 工种配置-新增员工
export function addWorkStaff(data) {
	return service({
		url: `${CPM}/meterReadingStaff/addWorkStaff`,
		method: 'post',
		data,
	})
}
// 工种配置-修改员工
export function updateWorkStaff(data) {
	return service({
		url: `${CPM}/meterReadingStaff/updateWorkStaff`,
		method: 'post',
		data,
	})
}

// 工种配置-员工分页信息列表分页查询
export function queryStaffPage(data) {
	return service({
		url: `${CPM}/meterReadingStaff/getWorkStaffPage`,
		method: 'post',
		data,
	})
}

// excel批量导入员工
export function batchImportStaff(data) {
	return service({
		url: `${CPM}/meterReadingStaff/importExcel`,
		method: 'post',
		data,
	})
}

// 员工导入Excel模板下载
export function downloadExcel() {
	return service({
		url: `${CPM}/meterReadingStaff/downloadExcel`,
		method: 'get',
		responseType: 'blob',
	})
}
// 用水量监控设置
export function submitUseMonitorParamsConfig(data) {
	return service({
		url: `${CPM}/businessHall/updateBusinessHallCode`,
		method: 'post',
		data,
	})
}
// 用水监控配置设置-- new
export function updateBusinessHallWaterAmount(params) {
	return service({
		url: `${CPM}/businessHall/updateBusinessHallWaterAmount`,
		method: 'get',
		params,
	})
}
