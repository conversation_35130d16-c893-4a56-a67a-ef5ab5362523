<template>
	<div class="page-layout">
		<div class="page-left">
			<GcModelHeader title="工种分类" :icon="require('@/assets/images/icon/title-structure.png')" />
			<div class="tree-list">
				<vue-scroll v-if="dataTree.length" :ops="{ bar: { background: '#e3e3e3' } }">
					<el-tree
						ref="staffTypeRef"
						:data="dataTree"
						default-expand-all
						highlight-current
						node-key="id"
						:props="{ label: 'name' }"
						:expand-on-click-node="false"
						@node-click="handleNodeClick"
					>
						<div class="custom-tree-node" slot-scope="{ node, data }">
							<div class="left-text">
								{{ data.name }}
							</div>
						</div>
					</el-tree>
				</vue-scroll>
				<div style="height: 100%" v-else>
					<GcEmpty />
				</div>
			</div>
		</div>
		<div class="page-right">
			<GcFormSimple ref="formRef" v-model="formData" :formItems="formItems" :formAttrs="formAttrs">
				<el-form-item>
					<el-button type="primary" @click="handleSearch">
						<i class="iconfontCis icon-small-search"></i>
						筛选
					</el-button>
					<el-button @click="handleReset">
						<i class="iconfontCis icon-reset"></i>
						重置
					</el-button>
				</el-form-item>
				<div style="float: right; display: flex; gap: 20px">
					<el-button type="primary" @click="handleDownload">下载模板</el-button>
					<GcUploadFileTwo
						v-has="'cpm_meterReadingStaff_importExcel'"
						v-model="staffList"
						name="staffExcel"
						:fieldData="{ staffType }"
						buttonName="导入员工"
						uploadApi="/cpm/meterReadingStaff/importExcel"
						@on-success="handleSearch"
					/>
					<el-button
						v-has="'cpm_meterReadingStaff_addWorkStaff'"
						type="primary"
						@click="handleSetStaff('add')"
					>
						新增员工
					</el-button>
				</div>
			</GcFormSimple>
			<GcTable
				ref="tableRef"
				:loading="loading"
				:columns="columns"
				:table-data="renderTableData"
				:page-size="pageData.size"
				:total="pageData.total"
				:current-page="pageData.current"
				showPage
				@current-page-change="handlePageChange"
			>
				<template v-slot:operate="{ row }">
					<el-button
						v-has="'cpm_meterReadingStaff_updateWorkStaff'"
						type="text"
						@click="handleSetStaff('edit', row)"
					>
						修改
					</el-button>
				</template>
			</GcTable>
		</div>
		<!-- 弹窗 -->
		<UpdateDialog ref="updateDialogRef" :show.sync="showUpdate" :editType="staffEdit" @success="getAllData" />
	</div>
</template>

<script>
import { getColumn } from './tableColumn'
import { ruleRequired } from '@/utils/rules.js'
import { removeNullParams, trimParams, exportBlob } from '@/utils/index.js'
import { queryStaffPage, downloadExcel } from '@/api/basicConfig.api.js'
import UpdateDialog from './components/UpdateDialog.vue'
import { apiGetDepartmentTree, apiGetBusinessHallCompanyMap } from '@/api/organizationManage.api.js'

export default {
	name: 'workConfig',
	components: { UpdateDialog },
	data() {
		return {
			staffType: '',
			dataTree:
				this.$store.getters?.dataList?.staffType?.map(item => {
					return {
						name: item.sortName,
						id: item.sortValue,
					}
				}) || [],

			showUpdate: false,
			staffEdit: 'add',
			staffList: [],
			// 左侧表单
			formData: {
				orgCode: '',
				staffName: '',
			},
			formItems: [
				{
					type: 'el-select',
					label: '营业分公司',
					prop: 'orgCode',
					options: [],
				},
				{
					type: 'el-input',
					label: '员工名称',
					prop: 'staffName',
				},
			],
			formAttrs: {
				inline: true,
				rules: {
					orgCode: [ruleRequired('请选择营业分公司')],
				},
			},
			// 右侧列表
			loading: false,
			columns: getColumn(this),
			tableData: [],
			pageData: {
				current: 1,
				size: 10,
				total: 0,
			},
			orgList: [],
			organizaTreeMap: {},
			treeList: [],
			orgCodeMap: [],
		}
	},
	computed: {
		orgMap() {
			let { orgList } = this
			orgList = orgList || []
			return orgList.reduce((acc, item) => {
				const { orgCode } = item
				acc[orgCode] = item
				return acc
			}, {})
		},
		renderTableData() {
			let { tableData, organizaTreeMap, orgMap } = this
			tableData = tableData || []
			organizaTreeMap = organizaTreeMap || {}

			return tableData.map(data => {
				const { orgCode, departmentCode } = data
				const orgItem = orgMap[orgCode]
				const departmentItem = organizaTreeMap[departmentCode]
				const orgName = orgItem ? orgItem.orgName : orgCode
				const departmentName = departmentItem ? departmentItem.label : departmentCode
				return {
					...data,
					orgName,
					departmentName,
				}
			})
		},
	},
	watch: {
		orgList: {
			handler: function (list) {
				list = list || []
				const el = this.formItems.find(item => {
					return item.prop === 'orgCode'
				})

				el.options = list.map(item => {
					return {
						label: item.orgName,
						value: item.orgCode,
					}
				})
				if (list.length > 0) {
					this.formData.orgCode = el.options[0].value
					this.getAllData()
				}
			},
			deep: true,
			immediate: true,
		},
	},
	activated() {
		this.getBusinessHallCompanyMap()
		this._getOrgStruData()
		this.getAllData(true)
	},
	methods: {
		// 初始化数据
		getAllData(flag) {
			if (this.staffType) {
				this.$refs.staffTypeRef.setCurrentKey(this.staffType)
			} else {
				this.handleNodeClick(this.dataTree[0])
			}
			if (!flag) {
				this.handleSearch()
			}
		},
		async getList() {
			this.loading = true
			try {
				const { current, size } = this.pageData
				const formParams = trimParams(removeNullParams(this.formData))
				Object.assign(formParams, { current, size, staffType: this.staffType })
				const { total = 0, records = [] } = await queryStaffPage(formParams)

				this.pageData.total = total
				this.tableData = records
			} catch (error) {
				this.tableData = []
				this.pageData = {
					current: 1,
					size: 10,
					total: 0,
				}
			} finally {
				this.loading = false
			}
		},
		handlePageChange({ page, size }) {
			this.pageData.current = page
			if (size) {
				this.pageData.size = size
			}
			this.getList()
		},
		handleReset() {
			this.$refs.formRef.clearValidate()
			this.$refs.formRef.resetFields()
			this.tableData = []
			this.pageData = {
				current: 1,
				size: 10,
				total: 0,
			}
			this.handleSearch()
		},
		async handleSearch() {
			const valid = await this.$refs.formRef.validate()
			if (!valid) return
			this.handlePageChange({ page: 1 })
		},
		handleNodeClick(data) {
			if (!data) return
			this.staffType = data.id
			this.$nextTick(() => {
				this.$refs.staffTypeRef.setCurrentKey(data.id)
				this.handleSearch()
			})
		},
		handleSetStaff(edit, data = {}) {
			this.staffEdit = edit
			this.showUpdate = true
			const newData = Object.assign(data, { staffType: this.staffType })
			this.$nextTick(() => {
				this.$refs.updateDialogRef.setFormData(newData)
			})
		},
		async handleDownload() {
			await downloadExcel().then(res => {
				exportBlob(res, '员工列表')
			})
		},
		getBusinessHallCompanyMap() {
			apiGetBusinessHallCompanyMap({}).then(result => {
				this.orgList = result || []
			})
		},
		formatOrganizeTreeList(data, map = {}, orgCodeMap = {}) {
			data = data || []
			return data.map(depData => {
				let {
					tenant_organization,
					department,
					children,
					department_code,
					department_name,
					parent_org_code,
				} = depData
				tenant_organization = tenant_organization || {}
				department = department || []
				children = children || []

				const value =
					department_code === undefined
						? `${tenant_organization.uid}_${tenant_organization.org_code}`
						: department_code
				const label = department_name === undefined ? tenant_organization.name : department_name
				const parentOrgCode = parent_org_code === undefined ? '' : parent_org_code
				const formatedChildList = this.formatOrganizeTreeList([...department, ...children], map, orgCodeMap)

				const item = {
					value,
					label,
					parentOrgCode,
					children: department_code === undefined ? formatedChildList : null,
				}

				map[value] = item

				if (tenant_organization.org_code) {
					orgCodeMap[tenant_organization.org_code] = item
				}

				return item
			})
		},
		// 获取所属部门
		async _getOrgStruData() {
			try {
				const result = await apiGetDepartmentTree({})
				const organizaTreeMap = {}
				const orgCodeMap = {}
				const treeList = this.formatOrganizeTreeList(result, organizaTreeMap, orgCodeMap)
				this.organizaTreeMap = organizaTreeMap
				this.treeList = treeList
				this.orgCodeMap = orgCodeMap
			} catch (e) {
				console.log(e)
			}
		},
	},
}
</script>
<style lang="scss" scoped>
.page-left {
	padding-top: 0;
	.model-header {
		padding: 0;
	}
}
.page-left {
	flex: 0 0 200px;
	.tree-list {
		width: 200px;
		height: calc(100% - 60px);
		overflow-x: auto;
		::v-deep .el-tree {
			.el-tree-node:focus > .el-tree-node__content {
				background-color: #fff;
			}
			.is-current {
				& > .el-tree-node__content {
					background-color: #eef5ff !important;
					.left-text {
						color: #2080f7;
					}
				}
			}
			.el-tree-node__content {
				line-height: 34px;
				height: 34px;
				.el-checkbox__inner {
					border-radius: 0;
				}
				.el-tree-node__expand-icon {
					&:not(.is-leaf) {
						color: #666;
					}
				}
				&:hover {
					.el-icon-more {
						display: block !important;
						color: #2f87fe;
					}
				}
			}
		}
		.custom-tree-node {
			width: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;
			gap: 20px;
		}
	}
}
</style>
