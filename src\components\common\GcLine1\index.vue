<template>
	<div class="gc-line1" ref="chartRef"></div>
</template>

<script>
import options from './options.js'
export default {
	name: 'Gcline1',
	props: {
		unit: String,
		xData: Array,
		seriesData: Array,
		showDataZoom: {
			type: Boolean,
			default: true,
		},
		axisLabelRotate: {
			type: Number,
			default: 40,
		},
		legend: Object,
		startDataZoom: Number,
		endDataZoom: Number,
	},
	components: {},
	data() {
		return {
			myChart: null,
		}
	},
	computed: {},
	watch: {
		seriesData: {
			handler: function (arr) {
				if (arr && arr.length) {
					this.init()
				}
			},
			deep: true,
		},
	},
	methods: {
		init() {
			this.myChart && this.myChart.clear()
			this.myChart && this.myChart.dispose()
			this.myChart = null
			this.myChart = window.echarts.init(this.$refs.chartRef)
			this.setOptions()
			this.handleResize()
			window.addEventListener('resize', this.handleResize)
		},
		handleResize() {
			if (this.myChart) {
				this.myChart.resize()
			}
		},
		setOptions() {
			const props = {
				xAxis: {
					data: this.xData,
				},
				yAxis: {
					name: this.unit,
				},
				seriesData: this.seriesData,
				showDataZoom: this.showDataZoom,
				axisLabelRotate: this.axisLabelRotate,
				legend: this.legend,
				startDataZoom: this.startDataZoom,
				endDataZoom: this.endDataZoom,
			}
			this.myChart && this.myChart.setOption(options(this.seriesData, props))
		},
	},
	beforeDestroy() {
		window.removeEventListener('resize', this.handleResize)
	},
}
</script>

<style lang="scss" scoped>
.gc-line1 {
	height: 100%;
}
</style>
